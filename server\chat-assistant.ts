import OpenAI from "openai";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { storage } from "./storage";
import type { Supplier } from "@shared/schema";

// the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export interface ChatResponse {
  message: string;
  suggestions?: string[];
  suppliers?: Supplier[];
  actionType?: 'search' | 'inquiry' | 'planning' | 'profile' | 'general';
}

export class HelloLuzonChatAssistant {
  private systemPrompt = `You are the AI assistant for "Hello Luzon!", a comprehensive local services marketplace in Luzon, starting with Region 2 (Cagayan, Isabela, Nueva Vizcaya, Quirino, Batanes). Your role is to help users find and connect with the right service providers quickly and effortlessly - from event planning to home repairs, personal services to business solutions.

Your key functions include:

1. **Natural Language Search & Recommendation**  
   When a user types a query like "Find me wedding caterers in Tuguegarao with a budget of 15,000 PHP", "I need an electrician in Isabela", or "Show me carpenters for home renovation in Cagayan," parse the intent and return a curated list of service providers that match the service type, location, and budget. Include provider names, brief descriptions, and contact info or inquiry links.

2. **Smart Inquiry Assistance**  
   Guide users in filling out booking or inquiry forms by asking relevant questions (service date, project scope, budget range, specific requirements) and pre-filling forms with gathered info to make contacting service providers easier.

3. **Supplier Profile Generation Support**  
   Help suppliers create compelling profile descriptions by generating professional bios and service summaries based on their inputs (e.g., years of experience, specialties, and customer highlights).

4. **Event Planning Suggestions**  
   Provide general event planning tips and suggestions tailored to the event type (e.g., weddings, birthdays, corporate events), including checklist reminders and timelines.

5. **Multi-Criteria Filtering**  
   Support users in refining search results by filtering suppliers based on criteria like price range, service category, location (province or city), and customer ratings.

6. **Feedback & Review Collection**  
   Prompt users to leave feedback or reviews after their event and summarize common positive or negative themes to help improve supplier listings.

7. **Localized Cultural Awareness**  
   Incorporate knowledge of local customs, venues, and event trends specific to Region 2 and wider Luzon to provide culturally relevant recommendations.

When responding, be clear, concise, and helpful. Always confirm user needs with follow-up questions if the request is ambiguous. Use friendly and engaging language that encourages users to explore local service providers and trust Hello Luzon! as their go-to marketplace.

**Response Format for Service Requests:**
When a user asks for a specific service, always:
1. Confirm the service type, location, and budget if provided
2. Ask for any missing crucial details (budget range, timeline, specific requirements)
3. Provide 3-5 curated recommendations with:
   - Business name and brief specialty description
   - Contact information (phone/email)
   - Why they're a good match for the user's needs
4. Offer to help send inquiries or provide more details

Example response format:
"I found these [service type] in [location] within your budget:
1. [Business Name] – [Brief description]. Contact: [phone/email]
2. [Business Name] – [Brief description]. Contact: [phone/email] 
3. [Business Name] – [Brief description]. Contact: [phone/email]

Would you like me to help send inquiries or get more details about any of these providers?"

Available service categories: Wedding Services, Catering, Photography, Event Planning, Decorations, Entertainment, Venues, Transportation, Electrician, Plumber, Carpenter, Woodworking, Painter, Mason & Construction, Roofing, Flooring, HVAC & Aircon, Landscaping & Gardening, Interior Design, Architecture, Cook & Chef, Housekeeping & Cleaning, Laundry Services, Childcare & Nanny, Elderly Care, Pet Care & Grooming, Tutoring & Education, Fitness & Personal Training, Beauty & Wellness, Massage Therapy, Accounting & Bookkeeping, Legal Services, IT & Computer Repair, Web Design & Development, Marketing & Advertising, Graphic Design, Translation Services, Business Consulting, Auto Repair & Mechanics, Appliance Repair, Solar Installation, Pest Control, Delivery & Logistics, Real Estate, Insurance Services, Financial Advisory

Available provinces: Cagayan, Isabela, Nueva Vizcaya, Quirino, Batanes

Always respond in JSON format with this structure:
{
  "message": "Your helpful response to the user",
  "suggestions": ["Optional follow-up suggestions"],
  "actionType": "search|inquiry|planning|profile|general",
  "searchCriteria": {
    "category": "category if search query",
    "location": "location if specified",
    "budget": "budget range if mentioned"
  }
}`;

  async chat(userMessage: string, conversationHistory: ChatMessage[] = []): Promise<ChatResponse> {
    try {
      // Get all suppliers for context
      const allSuppliers = await storage.getAllSuppliers();
      
      // Build conversation context
      const messages = [
        { role: "system" as const, content: this.systemPrompt },
        { role: "system" as const, content: `Available suppliers: ${JSON.stringify(allSuppliers.map(s => ({
          name: s.name,
          category: s.category,
          location: s.location,
          province: s.province,
          startingPrice: s.startingPrice,
          description: s.description.substring(0, 100)
        })), null, 2)}` },
        ...conversationHistory.map(msg => ({ role: msg.role, content: msg.content })),
        { role: "user" as const, content: userMessage }
      ];

      const completion = await openai.chat.completions.create({
        model: "gpt-4o",
        messages,
        response_format: { type: "json_object" },
        temperature: 0.7,
        max_tokens: 1000
      });

      const response = JSON.parse(completion.choices[0].message.content || '{}');
      
      // If this is a search query, find matching suppliers
      let matchingSuppliers: Supplier[] = [];
      if (response.actionType === 'search' && response.searchCriteria) {
        matchingSuppliers = await this.findMatchingSuppliers(response.searchCriteria, allSuppliers);
      }

      return {
        message: response.message || "I'm here to help you find the perfect suppliers for your event!",
        suggestions: response.suggestions || [],
        suppliers: matchingSuppliers,
        actionType: response.actionType || 'general'
      };

    } catch (error) {
      console.error("Chat assistant error:", error);
      return {
        message: "I'm here to help you with Hello Luzon! What kind of event suppliers are you looking for?",
        actionType: 'general'
      };
    }
  }

  private async findMatchingSuppliers(criteria: any, allSuppliers: Supplier[]): Promise<Supplier[]> {
    let filtered = allSuppliers;

    // Filter by category
    if (criteria.category) {
      filtered = filtered.filter(s => 
        s.category.toLowerCase().includes(criteria.category.toLowerCase())
      );
    }

    // Filter by location/province
    if (criteria.location) {
      filtered = filtered.filter(s => 
        s.location.toLowerCase().includes(criteria.location.toLowerCase()) ||
        s.province.toLowerCase().includes(criteria.location.toLowerCase())
      );
    }

    // Filter by budget (extract numbers from budget string)
    if (criteria.budget && typeof criteria.budget === 'string') {
      const budgetMatch = criteria.budget.match(/(\d+)/);
      if (budgetMatch) {
        const maxBudget = parseInt(budgetMatch[1]);
        filtered = filtered.filter(s => s.startingPrice <= maxBudget);
      }
    }

    // Sort by rating and featured status
    return filtered
      .sort((a, b) => {
        if (a.featured && !b.featured) return -1;
        if (!a.featured && b.featured) return 1;
        return parseFloat(b.rating) - parseFloat(a.rating);
      })
      .slice(0, 5); // Return top 5 matches
  }

  async generateSupplierProfile(supplierInfo: {
    businessName: string;
    category: string;
    experience: string;
    specialties: string;
    achievements?: string;
  }): Promise<string> {
    try {
      const prompt = `Generate a compelling supplier profile description for Hello Luzon! marketplace:

Business: ${supplierInfo.businessName}
Category: ${supplierInfo.category}
Experience: ${supplierInfo.experience}
Specialties: ${supplierInfo.specialties}
Achievements: ${supplierInfo.achievements || 'Not specified'}

Create a professional, engaging description that highlights their expertise and appeals to potential customers in Region 2, Philippines. Make it warm, trustworthy, and culturally relevant.`;

      const completion = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.8,
        max_tokens: 300
      });

      return completion.choices[0].message.content || "Professional event supplier ready to make your special day perfect.";
    } catch (error) {
      console.error("Profile generation error:", error);
      return "Professional event supplier ready to make your special day perfect.";
    }
  }

  async getEventPlanningTips(eventType: string, guestCount?: number, budget?: number): Promise<{
    tips: string[];
    timeline: string[];
    checklist: string[];
  }> {
    try {
      const prompt = `Provide event planning guidance for a ${eventType} in Region 2, Philippines:
${guestCount ? `Guest count: ${guestCount}` : ''}
${budget ? `Budget: ₱${budget}` : ''}

Provide practical tips, timeline, and checklist items specific to local customs and venues in Cagayan, Isabela, Nueva Vizcaya, Quirino, or Batanes.

Return JSON format:
{
  "tips": ["tip1", "tip2", "tip3"],
  "timeline": ["8 weeks before: task", "6 weeks before: task"],
  "checklist": ["item1", "item2", "item3"]
}`;

      const completion = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        response_format: { type: "json_object" },
        temperature: 0.7
      });

      return JSON.parse(completion.choices[0].message.content || '{"tips":[],"timeline":[],"checklist":[]}');
    } catch (error) {
      console.error("Event planning tips error:", error);
      return {
        tips: ["Start planning early", "Set a clear budget", "Create a guest list"],
        timeline: ["8 weeks before: Book venue", "6 weeks before: Send invitations"],
        checklist: ["Venue", "Catering", "Entertainment", "Decorations"]
      };
    }
  }
}

export const chatAssistant = new HelloLuzonChatAssistant();