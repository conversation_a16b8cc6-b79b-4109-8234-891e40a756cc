import OpenAI from "openai";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { storage } from "./storage";
import type { Supplier } from "@shared/schema";

// the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export interface UserPreferences {
  eventType?: string;
  budget?: number;
  guestCount?: number;
  location?: string;
  province?: string;
  style?: string;
  priorities?: string[];
  dateFlexibility?: 'strict' | 'flexible' | 'very_flexible';
  previousSuppliers?: string[];
  specialRequirements?: string[];
}

export interface RecommendationResult {
  supplier: Supplier;
  matchScore: number;
  reasons: string[];
  category: 'perfect_match' | 'great_option' | 'budget_friendly' | 'premium_choice';
  aiInsights: string;
}

export interface SmartRecommendations {
  recommendations: RecommendationResult[];
  summary: string;
  budgetAnalysis: {
    totalEstimate: number;
    breakdown: Array<{ category: string; estimate: number; suppliers: string[] }>;
    savingsTips: string[];
  };
  alternatives: RecommendationResult[];
  eventPlanningTips: string[];
}

export class RecommendationEngine {
  
  async getSmartRecommendations(preferences: UserPreferences): Promise<SmartRecommendations> {
    try {
      const allSuppliers = await storage.getAllSuppliers();
      
      // Use AI to analyze user preferences and generate smart recommendations
      const aiAnalysis = await this.analyzeUserPreferences(preferences, allSuppliers);
      
      // Score and rank suppliers
      const scoredSuppliers = await this.scoreSuppliers(preferences, allSuppliers, aiAnalysis);
      
      // Create recommendation categories
      const recommendations = this.categorizeRecommendations(scoredSuppliers);
      
      // Generate budget analysis
      const budgetAnalysis = await this.analyzeBudget(preferences, recommendations);
      
      // Get alternatives and tips
      const alternatives = this.getAlternatives(scoredSuppliers, recommendations);
      const eventPlanningTips = await this.generateEventTips(preferences);
      
      // Generate summary
      const summary = await this.generateRecommendationSummary(preferences, recommendations);
      
      return {
        recommendations: recommendations.slice(0, 8),
        summary,
        budgetAnalysis,
        alternatives: alternatives.slice(0, 4),
        eventPlanningTips
      };
      
    } catch (error) {
      console.error("Recommendation engine error:", error);
      return this.getFallbackRecommendations(preferences);
    }
  }

  private async analyzeUserPreferences(preferences: UserPreferences, suppliers: Supplier[]): Promise<any> {
    try {
      const prompt = `Analyze these user preferences for event planning in Region 2, Philippines:

User Preferences:
${JSON.stringify(preferences, null, 2)}

Available Suppliers:
${JSON.stringify(suppliers.map(s => ({
  name: s.name,
  category: s.category,
  location: s.location,
  province: s.province,
  startingPrice: s.startingPrice,
  description: s.description.substring(0, 150),
  verified: s.verified,
  rating: s.rating
})), null, 2)}

Provide intelligent analysis for supplier recommendations considering:
1. Event type compatibility
2. Budget alignment
3. Location preferences and logistics
4. Cultural considerations for Region 2
5. Seasonal factors
6. Quality indicators

Return JSON:
{
  "eventTypeInsights": "Analysis of event requirements",
  "budgetRecommendations": "Budget optimization suggestions",
  "locationFactors": "Geographic and logistical considerations", 
  "qualityPriorities": "What to prioritize for this event type",
  "culturalConsiderations": "Local customs and preferences",
  "riskFactors": "Potential challenges to consider"
}`;

      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        response_format: { type: "json_object" },
        temperature: 0.3,
      });

      return JSON.parse(response.choices[0].message.content || '{}');
    } catch (error) {
      console.error("AI analysis error:", error);
      return {
        eventTypeInsights: "Standard event planning considerations",
        budgetRecommendations: "Focus on value and quality",
        locationFactors: "Consider travel distance and accessibility",
        qualityPriorities: "Reliability and good reviews",
        culturalConsiderations: "Respect local traditions",
        riskFactors: "Book early for popular dates"
      };
    }
  }

  private async scoreSuppliers(preferences: UserPreferences, suppliers: Supplier[], aiAnalysis: any): Promise<Array<Supplier & { score: number; reasons: string[] }>> {
    const scoredSuppliers = suppliers.map(supplier => {
      let score = 0;
      const reasons: string[] = [];

      // Budget compatibility (30% weight)
      if (preferences.budget) {
        const budgetRatio = preferences.budget / supplier.startingPrice;
        if (budgetRatio >= 1.5) {
          score += 30;
          reasons.push("Well within budget with room for extras");
        } else if (budgetRatio >= 1.0) {
          score += 25;
          reasons.push("Fits your budget perfectly");
        } else if (budgetRatio >= 0.8) {
          score += 15;
          reasons.push("Slightly above budget but may be negotiable");
        }
      } else {
        score += 20; // Neutral score if no budget specified
      }

      // Location match (25% weight)
      if (preferences.location || preferences.province) {
        const locationMatch = 
          (preferences.location && supplier.location.toLowerCase().includes(preferences.location.toLowerCase())) ||
          (preferences.province && supplier.province.toLowerCase().includes(preferences.province.toLowerCase()));
        
        if (locationMatch) {
          score += 25;
          reasons.push(`Located in your preferred area: ${supplier.location}`);
        } else {
          // Check nearby provinces
          const nearbyScore = this.calculateLocationScore(preferences, supplier);
          score += nearbyScore;
          if (nearbyScore > 10) {
            reasons.push("Nearby location with easy access");
          }
        }
      } else {
        score += 15; // Neutral for no location preference
      }

      // Event type compatibility (20% weight)
      if (preferences.eventType) {
        const eventTypeMatch = this.checkEventTypeCompatibility(preferences.eventType, supplier);
        score += eventTypeMatch;
        if (eventTypeMatch >= 15) {
          reasons.push(`Specializes in ${preferences.eventType} events`);
        } else if (eventTypeMatch >= 10) {
          reasons.push(`Experienced with ${preferences.eventType} events`);
        }
      } else {
        score += 10;
      }

      // Quality indicators (15% weight)
      const rating = parseFloat(supplier.rating);
      if (rating >= 4.5) {
        score += 15;
        reasons.push(`Excellent ${rating}/5 star rating`);
      } else if (rating >= 4.0) {
        score += 12;
        reasons.push(`Great ${rating}/5 star rating`);
      } else if (rating >= 3.5) {
        score += 8;
        reasons.push(`Good ${rating}/5 star rating`);
      }

      // Verification and features (10% weight)
      if (supplier.verified) {
        score += 5;
        reasons.push("Verified trusted supplier");
      }
      if (supplier.featured) {
        score += 5;
        reasons.push("Featured premium supplier");
      }

      // Guest count compatibility
      if (preferences.guestCount) {
        const guestCompatibility = this.checkGuestCountCompatibility(preferences.guestCount, supplier);
        score += guestCompatibility;
        if (guestCompatibility > 0) {
          reasons.push("Perfect size for your guest count");
        }
      }

      return {
        ...supplier,
        score: Math.min(100, score), // Cap at 100
        reasons
      };
    });

    return scoredSuppliers.sort((a, b) => b.score - a.score);
  }

  private categorizeRecommendations(scoredSuppliers: Array<Supplier & { score: number; reasons: string[] }>): RecommendationResult[] {
    return scoredSuppliers.map(supplier => {
      let category: RecommendationResult['category'] = 'great_option';
      let aiInsights = '';

      if (supplier.score >= 85) {
        category = 'perfect_match';
        aiInsights = 'This supplier checks all your boxes and comes highly recommended.';
      } else if (supplier.score >= 70) {
        category = 'great_option';
        aiInsights = 'A solid choice that meets most of your requirements.';
      } else if (supplier.startingPrice < 15000) {
        category = 'budget_friendly';
        aiInsights = 'Great value option that can help you stay within budget.';
      } else if (supplier.startingPrice > 25000) {
        category = 'premium_choice';
        aiInsights = 'Premium service provider for a truly special event.';
      }

      return {
        supplier,
        matchScore: supplier.score,
        reasons: supplier.reasons,
        category,
        aiInsights
      };
    });
  }

  private async analyzeBudget(preferences: UserPreferences, recommendations: RecommendationResult[]): Promise<SmartRecommendations['budgetAnalysis']> {
    try {
      const prompt = `Analyze budget for event planning in Region 2, Philippines:

User Budget: ${preferences.budget || 'Not specified'}
Event Type: ${preferences.eventType || 'General'}
Guest Count: ${preferences.guestCount || 'Not specified'}

Recommended Suppliers:
${JSON.stringify(recommendations.slice(0, 5).map(r => ({
  name: r.supplier.name,
  category: r.supplier.category,
  price: r.supplier.startingPrice
})), null, 2)}

Provide budget analysis and money-saving tips specific to Philippines and Region 2.

Return JSON:
{
  "totalEstimate": "estimated total cost",
  "breakdown": [{"category": "Catering", "estimate": 20000, "suppliers": ["Supplier1"]}],
  "savingsTips": ["Tip 1", "Tip 2", "Tip 3"]
}`;

      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        response_format: { type: "json_object" },
        temperature: 0.3,
      });

      const analysis = JSON.parse(response.choices[0].message.content || '{}');
      
      return {
        totalEstimate: analysis.totalEstimate || (preferences.budget || 50000),
        breakdown: analysis.breakdown || [],
        savingsTips: analysis.savingsTips || [
          "Book suppliers early for better rates",
          "Consider weekday events for discounts",
          "Bundle services with multiple suppliers"
        ]
      };
    } catch (error) {
      console.error("Budget analysis error:", error);
      return {
        totalEstimate: preferences.budget || 50000,
        breakdown: [],
        savingsTips: [
          "Book suppliers early for better rates",
          "Consider off-peak dates for savings",
          "Compare multiple suppliers for best value"
        ]
      };
    }
  }

  private getAlternatives(scoredSuppliers: Array<Supplier & { score: number; reasons: string[] }>, mainRecommendations: RecommendationResult[]): RecommendationResult[] {
    const mainSupplierIds = mainRecommendations.map(r => r.supplier.id);
    const alternatives = scoredSuppliers
      .filter(s => !mainSupplierIds.includes(s.id))
      .slice(0, 6);

    return alternatives.map(supplier => ({
      supplier,
      matchScore: supplier.score,
      reasons: supplier.reasons,
      category: 'great_option' as const,
      aiInsights: 'Alternative option worth considering for your event.'
    }));
  }

  private async generateEventTips(preferences: UserPreferences): Promise<string[]> {
    try {
      const prompt = `Generate event planning tips for ${preferences.eventType || 'general event'} in Region 2, Philippines with ${preferences.guestCount || 'various'} guests. Focus on practical, actionable advice.`;

      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
        max_tokens: 200
      });

      const content = response.choices[0].message.content || '';
      return content.split('\n').filter(tip => tip.trim().length > 0).slice(0, 5);
    } catch (error) {
      return [
        "Start planning at least 8-12 weeks in advance",
        "Create a detailed timeline and checklist",
        "Always have a backup plan for outdoor events",
        "Confirm all bookings 1-2 weeks before the event",
        "Consider local weather patterns when choosing dates"
      ];
    }
  }

  private async generateRecommendationSummary(preferences: UserPreferences, recommendations: RecommendationResult[]): Promise<string> {
    try {
      const prompt = `Create a personalized summary for event recommendations:

User wants: ${preferences.eventType || 'event'} for ${preferences.guestCount || 'guests'} in ${preferences.location || preferences.province || 'Region 2'}
Budget: ${preferences.budget ? `₱${preferences.budget.toLocaleString()}` : 'Flexible'}

Top recommendations: ${recommendations.slice(0, 3).map(r => r.supplier.name).join(', ')}

Write a friendly, encouraging 2-3 sentence summary.`;

      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
        max_tokens: 150
      });

      return response.choices[0].message.content || 
        `I found some great suppliers for your ${preferences.eventType || 'event'}! Based on your preferences, I've curated a mix of highly-rated options that should make your event memorable.`;
    } catch (error) {
      return `I found some excellent suppliers for your ${preferences.eventType || 'event'}! These recommendations are tailored to your needs and budget.`;
    }
  }

  private checkEventTypeCompatibility(eventType: string, supplier: Supplier): number {
    const eventLower = eventType.toLowerCase();
    const supplierCategory = supplier.category.toLowerCase();
    const supplierDesc = supplier.description.toLowerCase();

    // Direct category matches
    if (eventLower.includes('wedding') && supplierCategory.includes('wedding')) return 20;
    if (eventLower.includes('corporate') && supplierDesc.includes('corporate')) return 18;
    if (eventLower.includes('birthday') && supplierDesc.includes('birthday')) return 18;
    if (eventLower.includes('catering') && supplierCategory.includes('catering')) return 20;
    if (eventLower.includes('photo') && supplierCategory.includes('photo')) return 20;
    if (eventLower.includes('music') && supplierCategory.includes('music')) return 20;

    // Partial matches
    if (supplierDesc.includes(eventLower)) return 15;
    
    return 5; // Base compatibility
  }

  private calculateLocationScore(preferences: UserPreferences, supplier: Supplier): number {
    // Simple distance-based scoring - in a real implementation, you'd use proper geolocation
    const userProvince = preferences.province?.toLowerCase() || '';
    const supplierProvince = supplier.province.toLowerCase();
    
    if (userProvince === supplierProvince) return 20;
    
    // Adjacent provinces in Region 2
    const adjacentProvinces: { [key: string]: string[] } = {
      'cagayan': ['isabela'],
      'isabela': ['cagayan', 'nueva vizcaya', 'quirino'],
      'nueva vizcaya': ['isabela', 'quirino'],
      'quirino': ['isabela', 'nueva vizcaya'],
      'batanes': ['cagayan']
    };
    
    if (adjacentProvinces[userProvince]?.includes(supplierProvince)) return 15;
    
    return 5; // Different region
  }

  private checkGuestCountCompatibility(guestCount: number, supplier: Supplier): number {
    // This would typically be based on supplier capacity data
    // For now, we'll use heuristics based on starting price and category
    
    if (guestCount <= 50) return 3;
    if (guestCount <= 100) return 5;
    if (guestCount <= 200) return 3;
    
    return 1;
  }

  private getFallbackRecommendations(preferences: UserPreferences): SmartRecommendations {
    return {
      recommendations: [],
      summary: "I'm working on finding the best suppliers for your event. Please try again in a moment!",
      budgetAnalysis: {
        totalEstimate: preferences.budget || 50000,
        breakdown: [],
        savingsTips: ["Plan ahead for better deals", "Compare multiple options"]
      },
      alternatives: [],
      eventPlanningTips: ["Start planning early", "Set a clear budget", "Book popular suppliers in advance"]
    };
  }
}

export const recommendationEngine = new RecommendationEngine();