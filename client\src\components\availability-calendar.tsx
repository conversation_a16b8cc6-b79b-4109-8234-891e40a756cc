import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Calendar, Clock, Plus, Edit, Trash2, Users, CheckCircle, XCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { format, addDays, startOfWeek, endOfWeek, isSameDay, parseISO } from "date-fns";

interface AvailabilitySlot {
  id: number;
  supplierId: number;
  date: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  isRecurring: boolean;
  recurringType?: string;
  maxBookings: number;
  currentBookings: number;
  notes?: string;
}

interface Booking {
  id: number;
  customerId: number;
  supplierId: number;
  serviceType: string;
  bookingDate: string;
  startTime: string;
  endTime: string;
  status: string;
  customerNotes?: string;
  supplierNotes?: string;
  estimatedPrice?: number;
  location?: string;
}

interface AvailabilityCalendarProps {
  supplierId: number;
  mode: 'view' | 'manage'; // view for customers, manage for suppliers
  onBookingSelect?: (slot: AvailabilitySlot) => void;
}

export default function AvailabilityCalendar({ supplierId, mode, onBookingSelect }: AvailabilityCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showSlotModal, setShowSlotModal] = useState(false);
  const [editingSlot, setEditingSlot] = useState<AvailabilitySlot | null>(null);
  
  const [newSlot, setNewSlot] = useState({
    date: '',
    startTime: '09:00',
    endTime: '17:00',
    maxBookings: 1,
    isRecurring: false,
    recurringType: 'weekly',
    notes: ''
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get start and end of current week
  const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 }); // Monday
  const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 });

  // Fetch availability data
  const { data: availability = [], isLoading } = useQuery({
    queryKey: ['/api/suppliers', supplierId, 'availability', weekStart.toISOString(), weekEnd.toISOString()],
    queryFn: () => apiRequest('GET', `/api/suppliers/${supplierId}/availability?startDate=${weekStart.toISOString()}&endDate=${weekEnd.toISOString()}`),
  });

  // Fetch bookings if in manage mode
  const { data: bookings = [] } = useQuery({
    queryKey: ['/api/suppliers', supplierId, 'bookings', weekStart.toISOString(), weekEnd.toISOString()],
    queryFn: () => apiRequest('GET', `/api/suppliers/${supplierId}/bookings?startDate=${weekStart.toISOString()}&endDate=${weekEnd.toISOString()}`),
    enabled: mode === 'manage'
  });

  // Create availability slot mutation
  const createSlotMutation = useMutation({
    mutationFn: async (slot: any) => {
      return apiRequest('POST', `/api/suppliers/${supplierId}/availability`, slot);
    },
    onSuccess: () => {
      toast({
        title: "Availability Created! ✅",
        description: "Your availability slot has been added to the calendar.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/suppliers', supplierId, 'availability'] });
      setShowSlotModal(false);
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: "Creation Failed",
        description: error.message || "Could not create availability slot.",
        variant: "destructive"
      });
    }
  });

  // Update availability slot mutation
  const updateSlotMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: number; updates: any }) => {
      return apiRequest('PUT', `/api/availability/${id}`, updates);
    },
    onSuccess: () => {
      toast({
        title: "Availability Updated! ✅",
        description: "Your availability slot has been updated.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/suppliers', supplierId, 'availability'] });
      setShowSlotModal(false);
      setEditingSlot(null);
      resetForm();
    }
  });

  // Delete availability slot mutation
  const deleteSlotMutation = useMutation({
    mutationFn: async (id: number) => {
      return apiRequest('DELETE', `/api/availability/${id}`);
    },
    onSuccess: () => {
      toast({
        title: "Availability Deleted! 🗑️",
        description: "The availability slot has been removed.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/suppliers', supplierId, 'availability'] });
    }
  });

  const resetForm = () => {
    setNewSlot({
      date: '',
      startTime: '09:00',
      endTime: '17:00',
      maxBookings: 1,
      isRecurring: false,
      recurringType: 'weekly',
      notes: ''
    });
  };

  const handleCreateSlot = () => {
    if (!newSlot.date || !newSlot.startTime || !newSlot.endTime) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    createSlotMutation.mutate({
      ...newSlot,
      date: new Date(newSlot.date).toISOString(),
    });
  };

  const handleEditSlot = (slot: AvailabilitySlot) => {
    setEditingSlot(slot);
    setNewSlot({
      date: format(parseISO(slot.date), 'yyyy-MM-dd'),
      startTime: slot.startTime,
      endTime: slot.endTime,
      maxBookings: slot.maxBookings,
      isRecurring: slot.isRecurring,
      recurringType: slot.recurringType || 'weekly',
      notes: slot.notes || ''
    });
    setShowSlotModal(true);
  };

  const handleUpdateSlot = () => {
    if (!editingSlot) return;

    updateSlotMutation.mutate({
      id: editingSlot.id,
      updates: {
        ...newSlot,
        date: new Date(newSlot.date).toISOString(),
      }
    });
  };

  const handleDeleteSlot = (slotId: number) => {
    if (window.confirm('Are you sure you want to delete this availability slot?')) {
      deleteSlotMutation.mutate(slotId);
    }
  };

  const navigateWeek = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => addDays(prev, direction === 'next' ? 7 : -7));
  };

  const getWeekDays = () => {
    const days = [];
    for (let i = 0; i < 7; i++) {
      days.push(addDays(weekStart, i));
    }
    return days;
  };

  const getSlotsForDate = (date: Date) => {
    return availability.filter((slot: AvailabilitySlot) => 
      isSameDay(parseISO(slot.date), date)
    );
  };

  const getBookingsForDate = (date: Date) => {
    return bookings.filter((booking: Booking) => 
      isSameDay(parseISO(booking.bookingDate), date)
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin w-8 h-8 border-4 border-filipino-orange border-t-transparent rounded-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5 text-filipino-orange" />
              {mode === 'manage' ? 'Manage Availability' : 'Available Times'}
            </CardTitle>
            <CardDescription>
              {format(weekStart, 'MMM dd')} - {format(weekEnd, 'MMM dd, yyyy')}
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => navigateWeek('prev')}>
              ←
            </Button>
            <Button variant="outline" size="sm" onClick={() => navigateWeek('next')}>
              →
            </Button>
            
            {mode === 'manage' && (
              <Dialog open={showSlotModal} onOpenChange={setShowSlotModal}>
                <DialogTrigger asChild>
                  <Button size="sm" className="bg-filipino-orange hover:bg-orange-600">
                    <Plus className="w-4 h-4 mr-1" />
                    Add Slot
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>
                      {editingSlot ? 'Edit Availability' : 'Add Availability Slot'}
                    </DialogTitle>
                    <DialogDescription>
                      Set your available times for customers to book appointments.
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="date">Date *</Label>
                      <Input
                        id="date"
                        type="date"
                        value={newSlot.date}
                        onChange={(e) => setNewSlot(prev => ({ ...prev, date: e.target.value }))}
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="startTime">Start Time *</Label>
                        <Input
                          id="startTime"
                          type="time"
                          value={newSlot.startTime}
                          onChange={(e) => setNewSlot(prev => ({ ...prev, startTime: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="endTime">End Time *</Label>
                        <Input
                          id="endTime"
                          type="time"
                          value={newSlot.endTime}
                          onChange={(e) => setNewSlot(prev => ({ ...prev, endTime: e.target.value }))}
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="maxBookings">Max Bookings</Label>
                      <Input
                        id="maxBookings"
                        type="number"
                        min="1"
                        value={newSlot.maxBookings}
                        onChange={(e) => setNewSlot(prev => ({ ...prev, maxBookings: parseInt(e.target.value) || 1 }))}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="notes">Notes (Optional)</Label>
                      <Input
                        id="notes"
                        placeholder="Special instructions or details"
                        value={newSlot.notes}
                        onChange={(e) => setNewSlot(prev => ({ ...prev, notes: e.target.value }))}
                      />
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        onClick={editingSlot ? handleUpdateSlot : handleCreateSlot}
                        disabled={createSlotMutation.isPending || updateSlotMutation.isPending}
                        className="flex-1 bg-filipino-orange hover:bg-orange-600"
                      >
                        {editingSlot ? 'Update Slot' : 'Create Slot'}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setShowSlotModal(false);
                          setEditingSlot(null);
                          resetForm();
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-7 gap-2">
          {/* Day headers */}
          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (
            <div key={day} className="text-center font-medium text-gray-600 py-2">
              {day}
            </div>
          ))}
          
          {/* Calendar days */}
          {getWeekDays().map(date => {
            const slots = getSlotsForDate(date);
            const dayBookings = getBookingsForDate(date);
            const isToday = isSameDay(date, new Date());
            
            return (
              <div
                key={date.toISOString()}
                className={`min-h-32 p-2 border rounded-lg ${
                  isToday ? 'bg-filipino-orange/5 border-filipino-orange' : 'border-gray-200'
                }`}
              >
                <div className={`text-sm font-medium mb-2 ${isToday ? 'text-filipino-orange' : 'text-gray-900'}`}>
                  {format(date, 'd')}
                </div>
                
                {/* Availability slots */}
                <div className="space-y-1">
                  {slots.map((slot: AvailabilitySlot) => (
                    <div
                      key={slot.id}
                      className={`text-xs p-1 rounded border cursor-pointer ${
                        slot.isAvailable 
                          ? 'bg-green-50 border-green-200 text-green-800' 
                          : 'bg-gray-50 border-gray-200 text-gray-600'
                      }`}
                      onClick={() => mode === 'view' && onBookingSelect?.(slot)}
                    >
                      <div className="flex items-center justify-between">
                        <span>{slot.startTime}-{slot.endTime}</span>
                        {mode === 'manage' && (
                          <div className="flex gap-1">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditSlot(slot);
                              }}
                              className="p-0.5 hover:bg-white rounded"
                            >
                              <Edit className="w-3 h-3" />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteSlot(slot.id);
                              }}
                              className="p-0.5 hover:bg-white rounded text-red-600"
                            >
                              <Trash2 className="w-3 h-3" />
                            </button>
                          </div>
                        )}
                      </div>
                      {slot.maxBookings > 1 && (
                        <div className="flex items-center gap-1 mt-1">
                          <Users className="w-3 h-3" />
                          <span>{slot.currentBookings}/{slot.maxBookings}</span>
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {/* Bookings for manage mode */}
                  {mode === 'manage' && dayBookings.map((booking: Booking) => (
                    <div
                      key={booking.id}
                      className={`text-xs p-1 rounded border ${getStatusColor(booking.status)}`}
                    >
                      <div className="flex items-center justify-between">
                        <span>{booking.startTime}-{booking.endTime}</span>
                        {booking.status === 'confirmed' && <CheckCircle className="w-3 h-3" />}
                        {booking.status === 'cancelled' && <XCircle className="w-3 h-3" />}
                      </div>
                      <div className="truncate">{booking.serviceType}</div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}