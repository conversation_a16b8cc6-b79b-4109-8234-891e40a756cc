import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Shield, CreditCard, Smartphone, Building2, Clock, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import type { Supplier } from "@shared/schema";

interface EscrowPaymentProps {
  supplier: Supplier;
  serviceDetails: {
    description: string;
    date: string;
    amount: number;
    location: string;
  };
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const PAYMENT_METHODS = [
  { id: 'gcash', name: 'GCash', icon: Smartphone, description: 'Digital wallet', fee: '₱15' },
  { id: 'maya', name: 'Maya (PayMaya)', icon: Smartphone, description: 'Digital wallet', fee: '₱15' },
  { id: 'bpi', name: 'BPI Online', icon: Building2, description: 'Bank transfer', fee: '₱25' },
  { id: 'bdo', name: 'BDO Online', icon: Building2, description: 'Bank transfer', fee: '₱25' },
  { id: 'metrobank', name: 'Metrobank', icon: Building2, description: 'Bank transfer', fee: '₱25' },
  { id: 'card', name: 'Credit/Debit Card', icon: CreditCard, description: 'Visa, Mastercard', fee: '3.5%' }
];

const ESCROW_STEPS = [
  { icon: Shield, title: "Secure Payment", description: "Your payment is held safely in escrow" },
  { icon: CheckCircle, title: "Service Delivered", description: "Supplier provides the agreed service" },
  { icon: Clock, title: "Confirmation Period", description: "24-hour window to confirm completion" },
  { icon: CreditCard, title: "Payment Released", description: "Funds released to supplier automatically" }
];

export default function EscrowPayment({ supplier, serviceDetails, isOpen, onClose, onSuccess }: EscrowPaymentProps) {
  const [selectedMethod, setSelectedMethod] = useState('');
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    email: '',
    phone: '',
    address: ''
  });
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  
  const { toast } = useToast();

  const serviceFee = Math.round(serviceDetails.amount * 0.07); // 7% service fee
  const processingFee = selectedMethod === 'card' ? Math.round(serviceDetails.amount * 0.035) : 
                       selectedMethod === 'gcash' || selectedMethod === 'maya' ? 15 : 25;
  const totalAmount = serviceDetails.amount + serviceFee + processingFee;

  const createEscrowPaymentMutation = useMutation({
    mutationFn: async (paymentData: any) => {
      const response = await apiRequest('/api/escrow/create-payment', {
        method: 'POST',
        body: JSON.stringify(paymentData)
      });
      return response;
    },
    onSuccess: (result) => {
      if (result.paymentUrl) {
        // Redirect to payment gateway
        window.open(result.paymentUrl, '_blank');
        toast({
          title: "Payment Initiated! 🔐",
          description: "Complete your payment in the new window. Your funds will be held securely in escrow.",
        });
        onSuccess();
      }
    },
    onError: (error) => {
      toast({
        title: "Payment Error",
        description: "Could not initiate escrow payment. Please try again or contact support.",
        variant: "destructive"
      });
    }
  });

  const handlePayment = () => {
    if (!selectedMethod || !customerInfo.name || !customerInfo.email || !agreeToTerms) {
      toast({
        title: "Missing Information",
        description: "Please fill all required fields and agree to terms.",
        variant: "destructive"
      });
      return;
    }

    createEscrowPaymentMutation.mutate({
      supplierId: supplier.id,
      supplierName: supplier.name,
      serviceDetails,
      paymentMethod: selectedMethod,
      customerInfo,
      amounts: {
        serviceAmount: serviceDetails.amount,
        serviceFee,
        processingFee,
        totalAmount
      }
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[95vh] overflow-y-auto">
        
        {/* Header */}
        <div className="bg-filipino-orange text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold flex items-center gap-2">
                <Shield className="w-6 h-6" />
                Secure Escrow Payment
              </h2>
              <p className="text-orange-100 mt-1">Your payment is protected until service completion</p>
            </div>
            <Button variant="ghost" onClick={onClose} className="text-white hover:bg-orange-600">
              ✕
            </Button>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-6 p-6">
          
          {/* Left Column - Service Details & Escrow Process */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Service Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="font-medium">Provider:</span>
                  <span>{supplier.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Service:</span>
                  <span>{serviceDetails.description}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Date:</span>
                  <span>{serviceDetails.date}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Location:</span>
                  <span>{serviceDetails.location}</span>
                </div>
                <Separator />
                <div className="flex justify-between text-lg font-semibold">
                  <span>Service Amount:</span>
                  <span>₱{serviceDetails.amount.toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>How Escrow Protection Works</CardTitle>
                <CardDescription>Your payment journey with Hello Luzon!</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {ESCROW_STEPS.map((step, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="bg-filipino-orange text-white rounded-full p-2 flex-shrink-0">
                        <step.icon className="w-4 h-4" />
                      </div>
                      <div>
                        <h4 className="font-medium">{step.title}</h4>
                        <p className="text-sm text-gray-600">{step.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Payment Form */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Choose Payment Method</CardTitle>
                <CardDescription>All payments are secured with 256-bit encryption</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-3">
                  {PAYMENT_METHODS.map((method) => (
                    <button
                      key={method.id}
                      onClick={() => setSelectedMethod(method.id)}
                      className={`p-4 border rounded-lg text-left transition-all ${
                        selectedMethod === method.id 
                          ? 'border-filipino-orange bg-orange-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <method.icon className="w-5 h-5 text-filipino-orange" />
                          <div>
                            <div className="font-medium">{method.name}</div>
                            <div className="text-sm text-gray-600">{method.description}</div>
                          </div>
                        </div>
                        <div className="text-sm text-gray-500">+{method.fee}</div>
                      </div>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Customer Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Full Name *</Label>
                    <Input
                      id="name"
                      value={customerInfo.name}
                      onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Juan Dela Cruz"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      value={customerInfo.phone}
                      onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="09XX XXX XXXX"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={customerInfo.email}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="address">Service Address</Label>
                  <Input
                    id="address"
                    value={customerInfo.address}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, address: e.target.value }))}
                    placeholder="Complete address for service delivery"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Breakdown</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Service Fee</span>
                  <span>₱{serviceDetails.amount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Hello Luzon! Service Fee (7%)</span>
                  <span>₱{serviceFee.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Payment Processing Fee</span>
                  <span>₱{processingFee.toLocaleString()}</span>
                </div>
                <Separator />
                <div className="flex justify-between text-lg font-bold">
                  <span>Total Amount</span>
                  <span>₱{totalAmount.toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>

            <div className="space-y-4">
              <label className="flex items-start gap-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={agreeToTerms}
                  onChange={(e) => setAgreeToTerms(e.target.checked)}
                  className="mt-1"
                />
                <span className="text-sm text-gray-600">
                  I agree to the <span className="text-filipino-orange underline">Terms of Service</span> and 
                  <span className="text-filipino-orange underline"> Escrow Protection Policy</span>. 
                  I understand my payment will be held securely until service completion.
                </span>
              </label>

              <Button 
                onClick={handlePayment}
                disabled={!selectedMethod || !customerInfo.name || !customerInfo.email || !agreeToTerms || createEscrowPaymentMutation.isPending}
                className="w-full bg-filipino-orange hover:bg-orange-600 text-white py-3"
                size="lg"
              >
                {createEscrowPaymentMutation.isPending ? (
                  "Initiating Secure Payment..."
                ) : (
                  <>
                    <Shield className="w-5 h-5 mr-2" />
                    Pay ₱{totalAmount.toLocaleString()} via Escrow
                  </>
                )}
              </Button>

              <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                <Shield className="w-4 h-4" />
                <span>Protected by 256-bit SSL encryption & Philippine banking security standards</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}