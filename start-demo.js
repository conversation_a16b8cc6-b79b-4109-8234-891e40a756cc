#!/usr/bin/env node

/**
 * Hello Luzon System Demo Starter
 *
 * This script demonstrates how to start the Hello Luzon system
 * and provides helpful guidance for setup.
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🌟 Hello Luzon System Demo Starter');
console.log('=====================================\n');

// Check if dependencies are installed
if (!fs.existsSync('node_modules')) {
  console.log('❌ Dependencies not found. Please run: npm install');
  process.exit(1);
}

// Check for environment variables
const requiredEnvVars = ['DATABASE_URL', 'OPENAI_API_KEY', 'GEMINI_API_KEY'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.log('⚠️  Missing required environment variables:');
  missingEnvVars.forEach(varName => {
    console.log(`   - ${varName}`);
  });
  console.log('\n📝 Please set these environment variables or create a .env file');
  console.log('   See .env.example for reference\n');

  console.log('🔧 Quick setup for testing (PowerShell):');
  console.log('   $env:DATABASE_URL="postgresql://user:pass@host:port/db"');
  console.log('   $env:OPENAI_API_KEY="sk-your-openai-key"');
  console.log('   $env:GEMINI_API_KEY="your-gemini-key"');
  console.log('   $env:NODE_ENV="development"\n');

  console.log('🔧 Quick setup for testing (Bash):');
  console.log('   export DATABASE_URL="postgresql://user:pass@host:port/db"');
  console.log('   export OPENAI_API_KEY="sk-your-openai-key"');
  console.log('   export GEMINI_API_KEY="your-gemini-key"');
  console.log('   export NODE_ENV="development"\n');

  // For demo purposes, set test values
  console.log('🧪 Setting test values for demonstration...');
  process.env.DATABASE_URL = process.env.DATABASE_URL || 'postgresql://test:test@localhost:5432/test';
  process.env.OPENAI_API_KEY = process.env.OPENAI_API_KEY || 'test-key';
  process.env.GEMINI_API_KEY = process.env.GEMINI_API_KEY || 'test-key';
  process.env.NODE_ENV = process.env.NODE_ENV || 'development';

  console.log('⚠️  Note: Using test values - system will show connection errors');
}

console.log('🚀 Starting Hello Luzon system...\n');

// Check TypeScript compilation
console.log('🔍 Checking TypeScript compilation...');
const tscCheck = spawn('npx', ['tsc', '--noEmit'], { stdio: 'pipe' });

tscCheck.stdout.on('data', (data) => {
  console.log(data.toString());
});

tscCheck.stderr.on('data', (data) => {
  console.log('⚠️  TypeScript issues found:');
  console.log(data.toString());
});

tscCheck.on('close', (code) => {
  if (code !== 0) {
    console.log('❌ TypeScript compilation has errors');
    console.log('📖 See RUN_SYSTEM.md for fixing guidance\n');
    console.log('🔄 Attempting to start anyway (may fail)...\n');
  } else {
    console.log('✅ TypeScript compilation successful\n');
  }

  // Start the server
  console.log('🌐 Starting server on http://localhost:5000...');
  const serverProcess = spawn('npx', ['tsx', 'server/index.ts'], {
    stdio: 'inherit',
    env: { ...process.env }
  });

  serverProcess.on('error', (error) => {
    console.error('❌ Failed to start server:', error.message);
    console.log('\n📖 Check RUN_SYSTEM.md for troubleshooting guidance');
  });

  serverProcess.on('close', (code) => {
    console.log(`\n🛑 Server process exited with code ${code}`);
    if (code !== 0) {
      console.log('📖 Check RUN_SYSTEM.md for troubleshooting guidance');
    }
  });

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    serverProcess.kill('SIGINT');
    process.exit(0);
  });
});

// Provide helpful information
setTimeout(() => {
  console.log('\n📋 System Information:');
  console.log('   • Frontend: React + TypeScript + Vite');
  console.log('   • Backend: Express.js + TypeScript');
  console.log('   • Database: PostgreSQL with Drizzle ORM');
  console.log('   • AI: OpenAI GPT-4o + Google Gemini');
  console.log('\n🌐 Once running, access the application at:');
  console.log('   • Local: http://localhost:5000');
  console.log('   • Network: http://0.0.0.0:5000');
  console.log('\n📖 For detailed setup instructions, see RUN_SYSTEM.md');
  console.log('🐛 For troubleshooting, check the console output above');
}, 2000);
