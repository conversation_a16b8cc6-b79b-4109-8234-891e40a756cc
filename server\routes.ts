import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertInquirySchema, insertReviewSchema, insertSupplierSchema } from "@shared/schema";
import { z } from "zod";
import multer from "multer";
import csv from "csv-parser";
import { aiAssistant } from "./ai-assistant";
import { chatAssistant } from "./chat-assistant";
import { recommendationEngine } from "./recommendation-engine";

// Configure multer for file uploads
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB limit
});

export async function registerRoutes(app: Express): Promise<Server> {
  // Get all suppliers
  app.get("/api/suppliers", async (req, res) => {
    try {
      const suppliers = await storage.getAllSuppliers();
      res.json(suppliers);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch suppliers" });
    }
  });

  // Get featured suppliers
  app.get("/api/suppliers/featured", async (req, res) => {
    try {
      const suppliers = await storage.getFeaturedSuppliers();
      res.json(suppliers);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch featured suppliers" });
    }
  });

  // Search suppliers
  app.get("/api/suppliers/search", async (req, res) => {
    try {
      const { q, category, province, minPrice, maxPrice } = req.query;
      
      const filters: any = {};
      if (category) filters.category = category as string;
      if (province) filters.province = province as string;
      if (minPrice) filters.minPrice = parseInt(minPrice as string);
      if (maxPrice) filters.maxPrice = parseInt(maxPrice as string);

      const suppliers = await storage.searchSuppliers(q as string || "", filters);
      res.json(suppliers);
    } catch (error) {
      res.status(500).json({ message: "Failed to search suppliers" });
    }
  });

  // Get single supplier
  app.get("/api/suppliers/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const supplier = await storage.getSupplier(id);
      
      if (!supplier) {
        return res.status(404).json({ message: "Supplier not found" });
      }

      res.json(supplier);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch supplier" });
    }
  });

  // Get suppliers by category
  app.get("/api/suppliers/category/:category", async (req, res) => {
    try {
      const { category } = req.params;
      const suppliers = await storage.getSuppliersByCategory(category);
      res.json(suppliers);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch suppliers by category" });
    }
  });

  // Get all categories
  app.get("/api/categories", async (req, res) => {
    try {
      const categories = await storage.getAllCategories();
      res.json(categories);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch categories" });
    }
  });

  // Get single category
  app.get("/api/categories/:slug", async (req, res) => {
    try {
      const { slug } = req.params;
      const category = await storage.getCategory(slug);
      
      if (!category) {
        return res.status(404).json({ message: "Category not found" });
      }

      res.json(category);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch category" });
    }
  });

  // Get reviews for supplier
  app.get("/api/suppliers/:id/reviews", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const reviews = await storage.getReviewsBySupplier(id);
      res.json(reviews);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch reviews" });
    }
  });

  // Create review
  app.post("/api/suppliers/:id/reviews", async (req, res) => {
    try {
      const supplierId = parseInt(req.params.id);
      const reviewData = insertReviewSchema.parse({
        ...req.body,
        supplierId
      });

      const review = await storage.createReview(reviewData);
      res.status(201).json(review);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid review data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create review" });
    }
  });

  // Create inquiry
  app.post("/api/suppliers/:id/inquiries", async (req, res) => {
    try {
      const supplierId = parseInt(req.params.id);
      const inquiryData = insertInquirySchema.parse({
        ...req.body,
        supplierId
      });

      const inquiry = await storage.createInquiry(inquiryData);
      res.status(201).json(inquiry);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid inquiry data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create inquiry" });
    }
  });

  // Get inquiries for supplier
  app.get("/api/suppliers/:id/inquiries", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const inquiries = await storage.getInquiriesBySupplier(id);
      res.json(inquiries);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch inquiries" });
    }
  });

  // Admin Routes
  // Add new supplier
  app.post("/api/admin/suppliers", async (req, res) => {
    try {
      const supplierData = insertSupplierSchema.parse(req.body);
      const supplier = await storage.createSupplier(supplierData);
      res.status(201).json(supplier);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid supplier data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create supplier" });
    }
  });

  // Smart AI-powered CSV import
  app.post("/api/admin/suppliers/import", upload.single('csv'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: "No CSV file provided" });
      }

      const csvData = req.file.buffer.toString();
      
      // Use AI to process and enhance CSV data
      const aiEnhancedSuppliers = await aiAssistant.processCSVData(csvData);
      
      const suppliers: any[] = [];
      let imported = 0;
      let enhanced = 0;

      for (const supplierData of aiEnhancedSuppliers) {
        try {
          // Remove AI suggestions for database insertion
          const { aiSuggestions, ...dbData } = supplierData;
          
          // Set defaults for required fields
          const supplierToInsert = {
            ...dbData,
            rating: 4.0, // Default rating
            startingPrice: dbData.startingPrice || 10000,
            verified: dbData.verified || false,
            featured: dbData.featured || false,
            website: dbData.website || null
          };

          // Validate and create supplier
          const validatedData = insertSupplierSchema.parse(supplierToInsert);
          const supplier = await storage.createSupplier(validatedData);
          suppliers.push({
            ...supplier,
            aiSuggestions
          });
          imported++;
          if (aiSuggestions) enhanced++;
        } catch (error) {
          console.error(`Failed to import supplier: ${supplierData.name}`, error);
        }
      }

      res.json({ 
        message: `Successfully imported ${imported} suppliers with AI enhancement`,
        imported,
        enhanced,
        suppliers 
      });
    } catch (error) {
      console.error("AI CSV import error:", error);
      res.status(500).json({ message: "Failed to process CSV with AI. Please check your file format." });
    }
  });

  // AI supplier enhancement endpoint
  app.post("/api/admin/suppliers/enhance", async (req, res) => {
    try {
      const supplierData = req.body;
      const enhanced = await aiAssistant.enhanceSupplierData(supplierData);
      res.json(enhanced);
    } catch (error) {
      console.error("AI enhancement error:", error);
      res.status(500).json({ message: "Failed to enhance supplier data" });
    }
  });

  // Smart search endpoint
  app.post("/api/suppliers/smart-search", async (req, res) => {
    try {
      const { query } = req.body;
      const allSuppliers = await storage.getAllSuppliers();
      const searchResults = await aiAssistant.smartSearch(query, allSuppliers);
      res.json(searchResults);
    } catch (error) {
      console.error("Smart search error:", error);
      res.status(500).json({ message: "Smart search temporarily unavailable" });
    }
  });

  // Data quality analysis
  app.get("/api/admin/data-quality", async (req, res) => {
    try {
      const allSuppliers = await storage.getAllSuppliers();
      const qualityReport = await aiAssistant.analyzeDataQuality(allSuppliers);
      res.json(qualityReport);
    } catch (error) {
      console.error("Data quality analysis error:", error);
      res.status(500).json({ message: "Failed to analyze data quality" });
    }
  });

  // Chat Assistant Routes
  app.post("/api/chat", async (req, res) => {
    try {
      const { message, conversationHistory = [] } = req.body;
      const response = await chatAssistant.chat(message, conversationHistory);
      res.json(response);
    } catch (error) {
      console.error("Chat error:", error);
      res.status(500).json({ 
        message: "I'm here to help you with Hello Luzon! What kind of event suppliers are you looking for?",
        actionType: 'general'
      });
    }
  });

  // Generate supplier profile
  app.post("/api/chat/generate-profile", async (req, res) => {
    try {
      const supplierInfo = req.body;
      const profile = await chatAssistant.generateSupplierProfile(supplierInfo);
      res.json({ profile });
    } catch (error) {
      console.error("Profile generation error:", error);
      res.status(500).json({ message: "Failed to generate profile" });
    }
  });

  // Supplier onboarding profile generation
  app.post("/api/suppliers/generate-profile", async (req, res) => {
    try {
      const { supplierInfo } = req.body;
      
      if (!supplierInfo || !supplierInfo.businessName || !supplierInfo.category) {
        return res.status(400).json({ error: "Business name and category are required" });
      }

      const result = await chatAssistant.generateSupplierProfile(supplierInfo);
      res.json(result);
    } catch (error) {
      console.error("Profile generation error:", error);
      res.status(500).json({ error: "Failed to generate supplier profile" });
    }
  });

  // Get feedback analysis for a supplier
  app.get("/api/suppliers/:id/feedback-analysis", async (req, res) => {
    try {
      const supplierId = parseInt(req.params.id);
      const supplier = await storage.getSupplier(supplierId);
      
      if (!supplier) {
        return res.status(404).json({ error: "Supplier not found" });
      }

      const reviews = await storage.getReviewsBySupplier(supplierId);
      const { feedbackAnalyzer } = await import("./feedback-analyzer");
      const analysis = await feedbackAnalyzer.generateSupplierSummary(reviews, supplier.category);
      
      res.json(analysis);
    } catch (error) {
      console.error("Feedback analysis error:", error);
      res.status(500).json({ error: "Failed to analyze feedback" });
    }
  });

  // Authentication Routes
  app.post("/api/auth/register", async (req, res) => {
    try {
      const { email, password, firstName, lastName, phone, userType } = req.body;
      
      if (!email || !password || !firstName || !lastName) {
        return res.status(400).json({ error: "Missing required fields" });
      }

      // Check if user already exists
      const existingUser = await storage.getUserByEmail(email);
      if (existingUser) {
        return res.status(400).json({ error: "An account with this email already exists" });
      }

      // Create new user
      const newUser = await storage.createUser({
        email,
        password, // In a real app, this should be hashed
        firstName,
        lastName,
        phone: phone || null,
        userType: userType || 'customer'
      });

      res.json({ 
        message: "Account created successfully", 
        user: { 
          id: newUser.id, 
          email: newUser.email, 
          firstName: newUser.firstName, 
          lastName: newUser.lastName,
          userType: newUser.userType 
        } 
      });
    } catch (error) {
      console.error("Registration error:", error);
      res.status(500).json({ error: "Failed to create account" });
    }
  });

  app.post("/api/auth/login", async (req, res) => {
    try {
      const { email, password } = req.body;
      
      if (!email || !password) {
        return res.status(400).json({ error: "Email and password are required" });
      }

      const user = await storage.getUserByEmail(email);
      if (!user || user.password !== password) { // In a real app, compare hashed passwords
        return res.status(401).json({ error: "Invalid email or password" });
      }

      // Update last login time
      await storage.updateUserLastLogin(user.id);

      res.json({ 
        message: "Login successful", 
        user: { 
          id: user.id, 
          email: user.email, 
          firstName: user.firstName, 
          lastName: user.lastName,
          userType: user.userType,
          supplierId: user.supplierId
        } 
      });
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({ error: "Login failed" });
    }
  });

  app.get("/api/auth/me", async (req, res) => {
    try {
      // In a real app, you'd get user ID from session/JWT
      // For now, return null (not authenticated)
      res.json({ user: null });
    } catch (error) {
      console.error("Auth check error:", error);
      res.status(500).json({ error: "Authentication check failed" });
    }
  });

  // Get event planning tips
  app.post("/api/chat/event-tips", async (req, res) => {
    try {
      const { eventType, guestCount, budget } = req.body;
      const tips = await chatAssistant.getEventPlanningTips(eventType, guestCount, budget);
      res.json(tips);
    } catch (error) {
      console.error("Event tips error:", error);
      res.status(500).json({ message: "Failed to get event planning tips" });
    }
  });

  // Advanced AI Recommendation System Routes
  app.post("/api/recommendations/smart", async (req, res) => {
    try {
      const preferences = req.body;
      const recommendations = await recommendationEngine.getSmartRecommendations(preferences);
      res.json(recommendations);
    } catch (error) {
      console.error("Smart recommendations error:", error);
      res.status(500).json({ 
        message: "Failed to generate recommendations",
        recommendations: [],
        summary: "Please try again with your preferences."
      });
    }
  });

  // Quick recommendation for specific event types
  app.get("/api/recommendations/quick/:eventType", async (req, res) => {
    try {
      const { eventType } = req.params;
      const { budget, location, guestCount } = req.query;
      
      const preferences = {
        eventType,
        budget: budget ? parseInt(budget as string) : undefined,
        location: location as string,
        guestCount: guestCount ? parseInt(guestCount as string) : undefined
      };
      
      const recommendations = await recommendationEngine.getSmartRecommendations(preferences);
      res.json(recommendations);
    } catch (error) {
      console.error("Quick recommendations error:", error);
      res.status(500).json({ message: "Failed to get quick recommendations" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
