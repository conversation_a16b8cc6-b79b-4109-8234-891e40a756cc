# Hello Luzon System - Setup and Run Guide

## Overview

Hello Luzon is a marketplace web application that connects people with local event suppliers in Region 2 of the Philippines (Cagayan, Isabela, Nueva Vizcaya, Quirino, Batanes). The system features AI-powered supplier management, smart search, recommendation engine, and real-time chat assistance.

## System Architecture

- **Frontend**: React + TypeScript + Vite + Tailwind CSS
- **Backend**: Express.js + TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **AI Services**: OpenAI GPT-4o + Google Gemini
- **Development**: Hot reload with Vite dev server

## Prerequisites

Before running the system, ensure you have:

1. **Node.js** (v18.18.0 or higher recommended)
2. **npm** (comes with Node.js)
3. **PostgreSQL database** (local or cloud)
4. **OpenAI API key** (for AI features)
5. **Google Gemini API key** (for recommendations)

## Required Environment Variables

The system requires these environment variables to function:

```bash
DATABASE_URL=postgresql://username:password@host:port/database_name
OPENAI_API_KEY=sk-your-openai-api-key-here
GEMINI_API_KEY=your-gemini-api-key-here
NODE_ENV=development
```

## Quick Start Guide

### 1. Install Dependencies

```bash
npm install
```

### 2. Set Up Environment Variables

Create a `.env` file in the root directory:

```bash
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/hello_luzon

# AI Service Keys
OPENAI_API_KEY=sk-your-openai-api-key-here
GEMINI_API_KEY=your-gemini-api-key-here

# Environment
NODE_ENV=development
```

### 3. Database Setup

If you have a PostgreSQL database ready:

```bash
# Push database schema
npm run db:push
```

### 4. Run the Development Server

**For Windows PowerShell:**
```powershell
$env:DATABASE_URL="your_database_url_here"
$env:OPENAI_API_KEY="your_openai_key_here"
$env:GEMINI_API_KEY="your_gemini_key_here"
$env:NODE_ENV="development"
npx tsx server/index.ts
```

**For Windows Command Prompt:**
```cmd
set DATABASE_URL=your_database_url_here
set OPENAI_API_KEY=your_openai_key_here
set GEMINI_API_KEY=your_gemini_key_here
set NODE_ENV=development
npx tsx server/index.ts
```

**For Linux/Mac:**
```bash
npm run dev
```

### 5. Access the Application

Once running, the application will be available at:
- **Local**: http://localhost:5000
- **Network**: http://0.0.0.0:5000

## Detailed Setup Instructions

### Database Setup Options

#### Option 1: Local PostgreSQL
1. Install PostgreSQL locally
2. Create a database named `hello_luzon`
3. Use connection string: `postgresql://username:password@localhost:5432/hello_luzon`

#### Option 2: Cloud Database (Recommended)
Popular cloud PostgreSQL providers:
- **Neon** (Free tier available): https://neon.tech/
- **Supabase** (Free tier available): https://supabase.com/
- **Railway** (Free tier available): https://railway.app/
- **Heroku Postgres**: https://www.heroku.com/postgres

#### Option 3: Docker PostgreSQL
```bash
docker run --name hello-luzon-db -e POSTGRES_PASSWORD=password -e POSTGRES_DB=hello_luzon -p 5432:5432 -d postgres:15
```

### Getting API Keys

#### OpenAI API Key
1. Visit https://platform.openai.com/
2. Sign up or log in
3. Go to API Keys section
4. Create a new API key
5. Copy the key (starts with `sk-`)

#### Google Gemini API Key
1. Visit https://makersuite.google.com/
2. Sign up or log in with Google account
3. Create a new API key
4. Copy the key

### Environment Variable Setup Methods

#### Method 1: .env File (Recommended)
Create a `.env` file in the project root:

```env
DATABASE_URL=postgresql://username:password@host:port/database_name
OPENAI_API_KEY=sk-your-openai-api-key-here
GEMINI_API_KEY=your-gemini-api-key-here
NODE_ENV=development
```

#### Method 2: System Environment Variables
**Windows:**
```powershell
[System.Environment]::SetEnvironmentVariable("DATABASE_URL", "your_database_url", "User")
[System.Environment]::SetEnvironmentVariable("OPENAI_API_KEY", "your_openai_key", "User")
[System.Environment]::SetEnvironmentVariable("GEMINI_API_KEY", "your_gemini_key", "User")
```

**Linux/Mac:**
```bash
export DATABASE_URL="your_database_url"
export OPENAI_API_KEY="your_openai_key"
export GEMINI_API_KEY="your_gemini_key"
```

## Available Scripts

```bash
# Development server with hot reload
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Type checking
npm run check

# Database schema push
npm run db:push
```

## System Features

### Core Features
- **Supplier Management**: Add, edit, and manage event suppliers
- **Smart Search**: AI-powered search with natural language processing
- **Recommendation Engine**: Personalized supplier recommendations
- **Review System**: Customer reviews and ratings
- **Inquiry Management**: Handle customer inquiries
- **CSV Import**: Bulk import suppliers with AI enhancement

### AI-Powered Features
- **Supplier Data Enhancement**: Automatically improve supplier descriptions
- **Smart Categorization**: AI-powered supplier categorization
- **Quality Analysis**: Data quality scoring and suggestions
- **Chat Assistant**: Real-time customer support
- **Recommendation Engine**: Personalized event planning assistance

### User Types
- **Customers**: Browse suppliers, make inquiries, leave reviews
- **Suppliers**: Manage profiles, respond to inquiries, view analytics
- **Admins**: System management, data quality oversight

## Troubleshooting

### Common Issues

#### 1. "OpenAI API Key Missing" Error
**Problem**: System fails to start with OpenAI key error
**Solution**:
- Verify your OpenAI API key is correct
- Ensure the key starts with `sk-`
- Check that the environment variable is properly set

#### 2. "Database Connection Failed" Error
**Problem**: Cannot connect to PostgreSQL database
**Solutions**:
- Verify database URL format: `postgresql://username:password@host:port/database`
- Ensure database server is running
- Check firewall settings
- Verify credentials

#### 3. "tsx command not found" Error
**Problem**: TypeScript execution fails
**Solution**: Use `npx tsx` instead of `tsx`

#### 4. Port 5000 Already in Use
**Problem**: Another service is using port 5000
**Solution**:
- Stop other services using port 5000
- Or modify the port in `server/index.ts`

#### 5. Module Resolution Errors
**Problem**: Cannot find modules or types
**Solution**:
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Development Tips

1. **Hot Reload**: The development server supports hot reload for both client and server code
2. **Database Seeding**: The system automatically seeds sample data on first run
3. **API Testing**: Use tools like Postman or curl to test API endpoints
4. **Logs**: Check console output for detailed error messages and API logs

## Production Deployment

### Environment Setup
```env
NODE_ENV=production
DATABASE_URL=your_production_database_url
OPENAI_API_KEY=your_openai_key
GEMINI_API_KEY=your_gemini_key
```

### Build and Deploy
```bash
# Build the application
npm run build

# Start production server
npm run start
```

### Deployment Platforms
- **Replit**: Already configured (see `.replit` file)
- **Vercel**: Supports Node.js applications
- **Railway**: PostgreSQL + Node.js hosting
- **Heroku**: Traditional PaaS deployment
- **DigitalOcean App Platform**: Container-based deployment

## API Endpoints

### Suppliers
- `GET /api/suppliers` - Get all suppliers
- `GET /api/suppliers/featured` - Get featured suppliers
- `POST /api/suppliers` - Create new supplier
- `PUT /api/suppliers/:id` - Update supplier
- `DELETE /api/suppliers/:id` - Delete supplier

### Search & Recommendations
- `GET /api/search?q=query` - Search suppliers
- `POST /api/search/smart` - AI-powered smart search
- `POST /api/recommendations` - Get personalized recommendations
- `GET /api/recommendations/quick/:eventType` - Quick event recommendations

### Reviews & Inquiries
- `GET /api/reviews/supplier/:id` - Get supplier reviews
- `POST /api/reviews` - Create review
- `GET /api/inquiries` - Get inquiries
- `POST /api/inquiries` - Create inquiry

### AI Features
- `POST /api/ai/enhance-supplier` - Enhance supplier data with AI
- `POST /api/ai/analyze-quality` - Analyze data quality
- `POST /api/chat` - Chat with AI assistant

## Security Considerations

1. **API Keys**: Never commit API keys to version control
2. **Database**: Use strong passwords and SSL connections
3. **Environment Variables**: Use secure methods to set production variables
4. **CORS**: Configure appropriate CORS settings for production
5. **Rate Limiting**: Consider implementing rate limiting for API endpoints

## Support and Maintenance

### Monitoring
- Monitor API usage and costs for OpenAI and Gemini
- Track database performance and storage usage
- Monitor application logs for errors

### Updates
- Regularly update dependencies for security patches
- Monitor AI model updates and pricing changes
- Backup database regularly

### Performance Optimization
- Implement caching for frequently accessed data
- Optimize database queries
- Consider CDN for static assets
- Monitor and optimize AI API usage

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with proper testing
4. Submit a pull request with detailed description

## License

This project is licensed under the MIT License.

---

## Running Without API Keys (Development Mode)

If you want to test the system without AI features, you can create a development version that bypasses API key requirements. However, note that AI-powered features will not work.

### Create Development Environment File

Create a `.env.development` file:

```env
DATABASE_URL=postgresql://username:password@localhost:5432/hello_luzon
NODE_ENV=development
# Leave AI keys empty for development
OPENAI_API_KEY=
GEMINI_API_KEY=
```

### Modify AI Services for Development

The system will need modifications to handle missing API keys gracefully in development mode. This would involve adding fallback responses for AI features when keys are not available.

## Sample Database Setup

If you need a quick database setup for testing, here's a sample using a free cloud provider:

### Using Neon (Free PostgreSQL)

1. Go to https://neon.tech/
2. Sign up for a free account
3. Create a new project
4. Copy the connection string
5. Use it as your `DATABASE_URL`

Example connection string format:
```
postgresql://username:<EMAIL>/neondb?sslmode=require
```

## Quick Test Run

For a quick test without setting up external services:

1. **Install dependencies**: `npm install`
2. **Set minimal environment variables**:
   ```powershell
   $env:NODE_ENV="development"
   $env:DATABASE_URL="postgresql://test:test@localhost:5432/test"
   $env:OPENAI_API_KEY="test"
   $env:GEMINI_API_KEY="test"
   ```
3. **Run with error handling**: The system will show specific errors for missing services
4. **Check logs**: Review console output to understand what services need setup

This approach helps identify exactly what needs to be configured for your specific use case.

## Current System Status

⚠️ **Important Note**: The current codebase has TypeScript compilation errors that need to be resolved before the system can run properly. Here's what needs to be fixed:

### Known Issues

1. **TypeScript Errors**: 93 compilation errors across multiple files
2. **Missing Schema Definitions**: Some database schema types are missing
3. **API Response Type Mismatches**: Frontend expects different response formats
4. **Database Connection Issues**: Missing database instance in storage class

### Immediate Steps to Fix

#### 1. Fix TypeScript Compilation
```bash
# Check all TypeScript errors
npx tsc --noEmit

# Focus on fixing these critical files first:
# - shared/schema.ts (add missing types)
# - server/storage.ts (fix database instance)
# - server/vite.ts (fix server options)
```

#### 2. Add Missing Database Schema
The system is missing several database table definitions in `shared/schema.ts`:
- `availabilitySlots`
- `bookings`
- `supplierSettings`

#### 3. Fix Database Storage Class
The `DatabaseStorage` class in `server/storage.ts` is missing the `db` property initialization.

### Working Components

Despite the compilation errors, these parts of the system are well-structured:

✅ **Package Configuration**: Dependencies are properly configured
✅ **Project Structure**: Clean separation of client/server/shared code
✅ **AI Integration**: OpenAI and Gemini integration is properly set up
✅ **Database Schema**: Core supplier/review/inquiry tables are defined
✅ **API Routes**: RESTful API structure is in place
✅ **Frontend Components**: React components are well-organized

### Recommended Development Approach

1. **Start with Backend**: Fix TypeScript errors in server files first
2. **Database First**: Ensure database connection and schema work
3. **API Testing**: Test API endpoints with tools like Postman
4. **Frontend Integration**: Fix frontend after backend is stable

### Alternative: Simplified Demo

If you want to see the system concept without fixing all errors, you could:

1. **Create a mock version** that bypasses database/AI calls
2. **Use static data** for demonstration purposes
3. **Focus on UI/UX** while backend is being fixed

### Getting Help

For development assistance:
1. **TypeScript Errors**: Use `npx tsc --noEmit` to see all issues
2. **Database Issues**: Check PostgreSQL connection and schema
3. **API Testing**: Use browser dev tools or Postman
4. **Build Issues**: Check `npm run build` output

The system has excellent potential once these technical issues are resolved!

## Demo Script

A demo script `start-demo.js` has been created to help with system startup:

```bash
# Run the demo script
node start-demo.js
```

This script will:
- Check for required dependencies
- Verify environment variables
- Provide setup guidance
- Attempt to start the system
- Show helpful error messages

## Summary

The Hello Luzon system is a sophisticated marketplace application with excellent architecture and features. While it currently has TypeScript compilation issues that prevent it from running, the foundation is solid:

### ✅ What's Working
- **Project Structure**: Well-organized codebase
- **Dependencies**: All packages properly configured
- **Documentation**: Comprehensive setup guide created
- **Environment Setup**: Clear configuration process
- **Demo Script**: Automated startup assistance

### 🔧 What Needs Fixing
- **TypeScript Errors**: 93 compilation errors to resolve
- **Database Schema**: Missing table definitions
- **API Types**: Response type mismatches
- **Storage Class**: Database instance initialization

### 🚀 Next Steps
1. **Fix TypeScript errors** starting with critical server files
2. **Complete database schema** with missing tables
3. **Test database connection** with real PostgreSQL instance
4. **Verify API endpoints** work correctly
5. **Test frontend integration** after backend is stable

### 📞 Support
For technical assistance with this system:
1. Review the TypeScript errors with `npx tsc --noEmit`
2. Check the detailed documentation in this file
3. Use the demo script for guided startup
4. Test individual components separately

The Hello Luzon marketplace has all the components needed for a successful event supplier platform once the technical issues are resolved!
