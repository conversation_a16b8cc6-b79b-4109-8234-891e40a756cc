import { useState, useRef, useEffect } from "react";
import { useMutation } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  MessageCircle, 
  Send, 
  X, 
  Bot, 
  User, 
  Sparkles,
  MapPin,
  Phone,
  Mail,
  ExternalLink
} from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import type { Supplier } from "@shared/schema";

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  suppliers?: Supplier[];
  suggestions?: string[];
}

interface ChatResponse {
  message: string;
  suggestions?: string[];
  suppliers?: Supplier[];
  actionType?: 'search' | 'inquiry' | 'planning' | 'profile' | 'general';
}

export default function AiChat() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      role: 'assistant',
      content: "Hi! I'm your Hello Luzon! AI assistant. I can help you find event suppliers, plan your events, or answer any questions about services in Region 2. What can I help you with today?",
      timestamp: new Date(),
      suggestions: [
        "Find wedding caterers in Tuguegarao",
        "Show me photographers in Isabela",
        "Help me plan a birthday party",
        "Event planning tips for corporate events"
      ]
    }
  ]);
  const [inputMessage, setInputMessage] = useState("");
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const chatMutation = useMutation({
    mutationFn: async (message: string) => {
      const conversationHistory = messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp
      }));
      
      const response = await apiRequest("POST", "/api/chat", {
        message,
        conversationHistory: conversationHistory.slice(-10) // Keep last 10 messages for context
      });
      return response.json() as Promise<ChatResponse>;
    },
    onSuccess: (response) => {
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: response.message,
        timestamp: new Date(),
        suppliers: response.suppliers,
        suggestions: response.suggestions
      };
      
      setMessages(prev => [...prev, assistantMessage]);
    },
    onError: () => {
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: "I'm sorry, I'm having trouble connecting right now. Please try again in a moment!",
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  });

  const sendMessage = () => {
    if (!inputMessage.trim() || chatMutation.isPending) return;

    const userMessage: ChatMessage = {
      role: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    chatMutation.mutate(inputMessage);
    setInputMessage("");
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputMessage(suggestion);
    sendMessage();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const SupplierCard = ({ supplier }: { supplier: Supplier }) => (
    <Card className="mb-3 hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex gap-4">
          <img 
            src={supplier.imageUrl} 
            alt={supplier.name}
            className="w-16 h-16 rounded-lg object-cover"
          />
          <div className="flex-1">
            <div className="flex items-start justify-between mb-2">
              <h4 className="font-semibold text-gray-900">{supplier.name}</h4>
              {supplier.verified && (
                <Badge className="bg-filipino-gold text-gray-900 text-xs">Verified</Badge>
              )}
            </div>
            <p className="text-sm text-gray-600 mb-2">
              {supplier.description.substring(0, 100)}...
            </p>
            <div className="flex items-center gap-4 text-xs text-gray-500 mb-2">
              <span className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                {supplier.location}
              </span>
              <span className="text-filipino-orange font-medium">
                From ₱{supplier.startingPrice.toLocaleString()}
              </span>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="outline" className="text-xs">
                <Phone className="h-3 w-3 mr-1" />
                Contact
              </Button>
              <Button size="sm" variant="outline" className="text-xs">
                <ExternalLink className="h-3 w-3 mr-1" />
                View Profile
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <>
      {/* Chat Toggle Button */}
      {!isOpen && (
        <Button
          onClick={() => setIsOpen(true)}
          className="fixed bottom-6 right-6 h-14 w-14 rounded-full bg-filipino-orange hover:bg-orange-600 shadow-lg z-50"
          size="icon"
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
      )}

      {/* Chat Window */}
      {isOpen && (
        <Card className="fixed bottom-6 right-6 w-96 h-[600px] shadow-xl z-50 flex flex-col">
          <CardHeader className="bg-filipino-orange text-white rounded-t-lg">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Bot className="h-5 w-5" />
                Hello Luzon! AI Assistant
              </CardTitle>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsOpen(false)}
                className="text-white hover:bg-orange-600"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="flex-1 p-0 flex flex-col">
            {/* Messages Area */}
            <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
              <div className="space-y-4">
                {messages.map((message, index) => (
                  <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] ${message.role === 'user' ? 'order-2' : 'order-1'}`}>
                      <div className={`flex items-start gap-2 ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          message.role === 'user' 
                            ? 'bg-filipino-orange text-white' 
                            : 'bg-gray-100 text-filipino-orange'
                        }`}>
                          {message.role === 'user' ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                        </div>
                        <div className={`rounded-lg p-3 ${
                          message.role === 'user' 
                            ? 'bg-filipino-orange text-white' 
                            : 'bg-gray-100 text-gray-900'
                        }`}>
                          <p className="text-sm">{message.content}</p>
                        </div>
                      </div>
                      
                      {/* Display suppliers if any */}
                      {message.suppliers && message.suppliers.length > 0 && (
                        <div className="mt-3">
                          <p className="text-xs text-gray-500 mb-2">Found {message.suppliers.length} suppliers:</p>
                          {message.suppliers.map((supplier) => (
                            <SupplierCard key={supplier.id} supplier={supplier} />
                          ))}
                        </div>
                      )}
                      
                      {/* Display suggestions */}
                      {message.suggestions && message.suggestions.length > 0 && (
                        <div className="mt-3 space-y-1">
                          <p className="text-xs text-gray-500 mb-2">Suggestions:</p>
                          {message.suggestions.map((suggestion, idx) => (
                            <Button
                              key={idx}
                              variant="outline"
                              size="sm"
                              className="text-xs mr-2 mb-1"
                              onClick={() => handleSuggestionClick(suggestion)}
                            >
                              <Sparkles className="h-3 w-3 mr-1" />
                              {suggestion}
                            </Button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                
                {chatMutation.isPending && (
                  <div className="flex justify-start">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-gray-100 text-filipino-orange flex items-center justify-center">
                        <Bot className="h-4 w-4" />
                      </div>
                      <div className="bg-gray-100 rounded-lg p-3">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-filipino-orange rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-filipino-orange rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-filipino-orange rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>

            {/* Input Area */}
            <div className="p-4 border-t">
              <div className="flex gap-2">
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask me anything about event suppliers..."
                  className="flex-1"
                  disabled={chatMutation.isPending}
                />
                <Button
                  onClick={sendMessage}
                  disabled={!inputMessage.trim() || chatMutation.isPending}
                  className="bg-filipino-orange hover:bg-orange-600"
                  size="icon"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
}