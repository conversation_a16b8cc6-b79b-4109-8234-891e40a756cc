# Hello Luzon Environment Configuration
# Copy this file to .env and fill in your actual values

# Database Configuration
# PostgreSQL connection string
# Format: postgresql://username:password@host:port/database_name
DATABASE_URL=postgresql://username:password@localhost:5432/hello_luzon

# AI Service API Keys
# OpenAI API key for AI-powered features (get from https://platform.openai.com/)
OPENAI_API_KEY=sk-your-openai-api-key-here

# Google Gemini API key for recommendations (get from https://makersuite.google.com/)
GEMINI_API_KEY=your-gemini-api-key-here

# Application Environment
NODE_ENV=development

# Optional: Custom Port (default is 5000)
# PORT=5000

# Optional: Session Secret (for production)
# SESSION_SECRET=your-random-session-secret-here
