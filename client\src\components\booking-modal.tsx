import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, MapPin, DollarSign, User, Phone, Mail } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { format, parseISO } from "date-fns";

interface AvailabilitySlot {
  id: number;
  supplierId: number;
  date: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  maxBookings: number;
  currentBookings: number;
  notes?: string;
}

interface Supplier {
  id: number;
  name: string;
  category: string;
  location: string;
  province: string;
  phone: string;
  email: string;
  startingPrice?: number;
  imageUrl?: string;
}

interface BookingModalProps {
  isOpen: boolean;
  onClose: () => void;
  slot: AvailabilitySlot | null;
  supplier: Supplier;
  currentUser?: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };
}

export default function BookingModal({ isOpen, onClose, slot, supplier, currentUser }: BookingModalProps) {
  const [bookingData, setBookingData] = useState({
    serviceType: '',
    customerNotes: '',
    estimatedPrice: '',
    location: '',
    contactName: currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : '',
    contactEmail: currentUser?.email || '',
    contactPhone: currentUser?.phone || '',
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const createBookingMutation = useMutation({
    mutationFn: async (booking: any) => {
      return apiRequest('POST', '/api/bookings', booking);
    },
    onSuccess: () => {
      toast({
        title: "Booking Request Sent! 📅",
        description: "Your booking request has been sent to the supplier. They will contact you soon to confirm.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/suppliers', supplier.id, 'availability'] });
      onClose();
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: "Booking Failed",
        description: error.message || "Could not create booking request. Please try again.",
        variant: "destructive"
      });
    }
  });

  const resetForm = () => {
    setBookingData({
      serviceType: '',
      customerNotes: '',
      estimatedPrice: '',
      location: '',
      contactName: currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : '',
      contactEmail: currentUser?.email || '',
      contactPhone: currentUser?.phone || '',
    });
  };

  const handleSubmitBooking = () => {
    if (!slot || !bookingData.serviceType || !bookingData.contactName || !bookingData.contactEmail) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    const booking = {
      customerId: currentUser?.id || null,
      supplierId: supplier.id,
      availabilitySlotId: slot.id,
      serviceType: bookingData.serviceType,
      bookingDate: slot.date,
      startTime: slot.startTime,
      endTime: slot.endTime,
      customerNotes: bookingData.customerNotes,
      estimatedPrice: bookingData.estimatedPrice ? parseInt(bookingData.estimatedPrice) : null,
      location: bookingData.location || supplier.location,
      status: 'pending'
    };

    createBookingMutation.mutate(booking);
  };

  if (!slot) return null;

  const isFullyBooked = slot.currentBookings >= slot.maxBookings;
  const availableSpots = slot.maxBookings - slot.currentBookings;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-filipino-orange" />
            Book Appointment with {supplier.name}
          </DialogTitle>
          <DialogDescription>
            Complete your booking request for this service appointment.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Booking Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-3">Appointment Details</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-500" />
                <span>{format(parseISO(slot.date), 'EEEE, MMMM d, yyyy')}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-gray-500" />
                <span>{slot.startTime} - {slot.endTime}</span>
              </div>
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-500" />
                <span>{supplier.name}</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-gray-500" />
                <span>{supplier.location}, {supplier.province}</span>
              </div>
            </div>
            
            {availableSpots > 0 ? (
              <Badge variant="secondary" className="mt-3">
                {availableSpots} spot{availableSpots > 1 ? 's' : ''} available
              </Badge>
            ) : (
              <Badge variant="destructive" className="mt-3">
                Fully booked
              </Badge>
            )}
            
            {slot.notes && (
              <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded text-sm">
                <strong>Supplier Note:</strong> {slot.notes}
              </div>
            )}
          </div>

          {isFullyBooked ? (
            <div className="text-center py-8">
              <p className="text-gray-600 mb-4">This time slot is fully booked.</p>
              <Button variant="outline" onClick={onClose}>
                Choose Different Time
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Service Details */}
              <div>
                <Label htmlFor="serviceType">Service Type *</Label>
                <Select
                  value={bookingData.serviceType}
                  onValueChange={(value) => setBookingData(prev => ({ ...prev, serviceType: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select the service you need" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="consultation">Consultation</SelectItem>
                    <SelectItem value="wedding">Wedding Services</SelectItem>
                    <SelectItem value="photography">Photography</SelectItem>
                    <SelectItem value="catering">Catering</SelectItem>
                    <SelectItem value="decoration">Event Decoration</SelectItem>
                    <SelectItem value="entertainment">Entertainment</SelectItem>
                    <SelectItem value="venue">Venue Booking</SelectItem>
                    <SelectItem value="transportation">Transportation</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="estimatedPrice">Budget Range (₱)</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="estimatedPrice"
                      type="number"
                      placeholder="Your budget"
                      value={bookingData.estimatedPrice}
                      onChange={(e) => setBookingData(prev => ({ ...prev, estimatedPrice: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="location">Event Location</Label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="location"
                      placeholder={supplier.location}
                      value={bookingData.location}
                      onChange={(e) => setBookingData(prev => ({ ...prev, location: e.target.value }))}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="border-t pt-4">
                <h3 className="font-semibold mb-3">Contact Information</h3>
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="contactName">Full Name *</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="contactName"
                        placeholder="Your full name"
                        value={bookingData.contactName}
                        onChange={(e) => setBookingData(prev => ({ ...prev, contactName: e.target.value }))}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="contactEmail">Email Address *</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="contactEmail"
                          type="email"
                          placeholder="<EMAIL>"
                          value={bookingData.contactEmail}
                          onChange={(e) => setBookingData(prev => ({ ...prev, contactEmail: e.target.value }))}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="contactPhone">Phone Number</Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="contactPhone"
                          placeholder="09XX XXX XXXX"
                          value={bookingData.contactPhone}
                          onChange={(e) => setBookingData(prev => ({ ...prev, contactPhone: e.target.value }))}
                          className="pl-10"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Notes */}
              <div>
                <Label htmlFor="customerNotes">Additional Details (Optional)</Label>
                <Textarea
                  id="customerNotes"
                  placeholder="Tell the supplier about your specific needs, preferences, or any special requirements..."
                  value={bookingData.customerNotes}
                  onChange={(e) => setBookingData(prev => ({ ...prev, customerNotes: e.target.value }))}
                  rows={3}
                />
              </div>

              {/* Pricing Info */}
              {supplier.startingPrice && (
                <div className="bg-yellow-50 border border-yellow-200 p-3 rounded">
                  <p className="text-sm text-yellow-800">
                    <strong>Starting Price:</strong> ₱{supplier.startingPrice.toLocaleString()} 
                    <span className="ml-2 text-xs">Final price will be confirmed by the supplier</span>
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-3">
                <Button
                  onClick={handleSubmitBooking}
                  disabled={createBookingMutation.isPending}
                  className="flex-1 bg-filipino-orange hover:bg-orange-600"
                >
                  {createBookingMutation.isPending ? "Sending Request..." : "Send Booking Request"}
                </Button>
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
              </div>

              <p className="text-xs text-gray-600 text-center">
                By submitting this request, you agree to be contacted by the supplier regarding this booking.
                No payment is required at this time.
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}