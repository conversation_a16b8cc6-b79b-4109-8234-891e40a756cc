@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 20 14.3% 4.1%;
  --muted: 60 4.8% 95.9%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 0 0% 100%;
  --popover-foreground: 20 14.3% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 20 14.3% 4.1%;
  --border: 20 5.9% 90%;
  --input: 20 5.9% 90%;
  --primary: 23 83% 46%; /* Filipino Orange #D2691E */
  --primary-foreground: 0 0% 98%;
  --secondary: 60 4.8% 95.9%;
  --secondary-foreground: 24 9.8% 10%;
  --accent: 60 4.8% 95.9%;
  --accent-foreground: 24 9.8% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 20 14.3% 4.1%;
  --radius: 0.5rem;
  
  /* Filipino-inspired colors */
  --filipino-orange: 23 83% 46%; /* #D2691E */
  --filipino-green: 105 32% 19%; /* #2C5530 */
  --filipino-gold: 51 100% 50%; /* #FFD700 */
  --warm-gray: 210 11% 98%; /* #F8F9FA */
}

.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 23 83% 46%; /* Filipino Orange */
  --primary-foreground: 0 0% 98%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

@layer utilities {
  .bg-filipino-orange {
    background-color: hsl(var(--filipino-orange));
  }
  
  .text-filipino-orange {
    color: hsl(var(--filipino-orange));
  }
  
  .bg-filipino-green {
    background-color: hsl(var(--filipino-green));
  }
  
  .text-filipino-green {
    color: hsl(var(--filipino-green));
  }
  
  .bg-filipino-gold {
    background-color: hsl(var(--filipino-gold));
  }
  
  .text-filipino-gold {
    color: hsl(var(--filipino-gold));
  }
  
  .bg-warm-gray {
    background-color: hsl(var(--warm-gray));
  }
  
  .font-poppins {
    font-family: 'Poppins', sans-serif;
  }
  
  .font-inter {
    font-family: 'Inter', sans-serif;
  }
}
