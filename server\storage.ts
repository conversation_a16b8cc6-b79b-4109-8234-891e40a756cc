import {
  suppliers,
  reviews,
  inquiries,
  categories,
  users,
  availabilitySlots,
  bookings,
  supplierSettings,
  type Supplier,
  type Review,
  type Inquiry,
  type Category,
  type User,
  type AvailabilitySlot,
  type Booking,
  type SupplierSettings,
  type InsertSupplier,
  type InsertReview,
  type InsertInquiry,
  type InsertCategory,
  type InsertUser,
  type InsertAvailabilitySlot,
  type InsertBooking,
  type InsertSupplierSettings
} from "@shared/schema";
import { drizzle } from "drizzle-orm/postgres-js";
import { eq, like, and, gte, lte, sql } from "drizzle-orm";
import postgres from "postgres";

export interface IStorage {
  // Users
  getUser(id: number): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUserLastLogin(id: number): Promise<void>;

  // Suppliers
  getAllSuppliers(): Promise<Supplier[]>;
  getSupplier(id: number): Promise<Supplier | undefined>;
  getSuppliersByCategory(category: string): Promise<Supplier[]>;
  getFeaturedSuppliers(): Promise<Supplier[]>;
  searchSuppliers(query: string, filters?: {
    category?: string;
    province?: string;
    minPrice?: number;
    maxPrice?: number;
  }): Promise<Supplier[]>;
  createSupplier(supplier: InsertSupplier): Promise<Supplier>;

  // Reviews
  getReviewsBySupplier(supplierId: number): Promise<Review[]>;
  createReview(review: InsertReview): Promise<Review>;

  // Inquiries
  getInquiriesBySupplier(supplierId: number): Promise<Inquiry[]>;
  createInquiry(inquiry: InsertInquiry): Promise<Inquiry>;

  // Categories
  getAllCategories(): Promise<Category[]>;
  getCategory(slug: string): Promise<Category | undefined>;
  createCategory(category: InsertCategory): Promise<Category>;

  // Availability Calendar System
  getSupplierAvailability(supplierId: number, startDate: Date, endDate: Date): Promise<AvailabilitySlot[]>;
  createAvailabilitySlot(slot: InsertAvailabilitySlot): Promise<AvailabilitySlot>;
  updateAvailabilitySlot(id: number, updates: Partial<InsertAvailabilitySlot>): Promise<void>;
  deleteAvailabilitySlot(id: number): Promise<void>;

  // Bookings
  getSupplierBookings(supplierId: number, startDate?: Date, endDate?: Date): Promise<Booking[]>;
  getCustomerBookings(customerId: number): Promise<Booking[]>;
  createBooking(booking: InsertBooking): Promise<Booking>;
  updateBookingStatus(id: number, status: string, supplierNotes?: string): Promise<void>;

  // Supplier Settings
  getSupplierSettings(supplierId: number): Promise<SupplierSettings | undefined>;
  createOrUpdateSupplierSettings(settings: InsertSupplierSettings): Promise<SupplierSettings>;
}

// Initialize PostgreSQL connection
const client = postgres(process.env.DATABASE_URL!);
const db = drizzle(client);

export class DatabaseStorage implements IStorage {
  private isSeeded = false;
  private db: ReturnType<typeof drizzle>;

  constructor() {
    this.db = db;
    this.initialize();
  }

  private async initialize() {
    if (!this.isSeeded) {
      await this.seedData();
      this.isSeeded = true;
    }
  }

  private async seedData() {
    try {
      // Check if data already exists
      const existingCategories = await db.select().from(categories).limit(1);
      if (existingCategories.length > 0) {
        return; // Already seeded
      }

      // Seed categories
      const categoriesData: InsertCategory[] = [
        {
          name: "Wedding Services",
          slug: "wedding-services",
          description: "Complete wedding planning and services",
          imageUrl: "https://images.unsplash.com/photo-1519741497674-611481863552?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300"
        },
        {
          name: "Catering",
          slug: "catering",
          description: "Professional catering services",
          imageUrl: "https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300"
        },
        {
          name: "Photography",
          slug: "photography",
          description: "Event and wedding photography",
          imageUrl: "https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300"
        },
        {
          name: "Event Planning",
          slug: "event-planning",
          description: "Complete event coordination",
          imageUrl: "https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300"
        },
        {
          name: "Decorations",
          slug: "decorations",
          description: "Event decorations and styling",
          imageUrl: "https://images.unsplash.com/photo-1530103862676-de8c9debad1d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300"
        },
        {
          name: "Entertainment",
          slug: "entertainment",
          description: "Live entertainment and performers",
          imageUrl: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300"
        },
        {
          name: "Venues",
          slug: "venues",
          description: "Event venues and locations",
          imageUrl: "https://images.unsplash.com/photo-1519167758481-83f550bb49b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300"
        },
        {
          name: "Transportation",
          slug: "transportation",
          description: "Event transportation services",
          imageUrl: "https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300"
        }
      ];

      await db.insert(categories).values(categoriesData);

      // Seed suppliers
      const suppliersData: InsertSupplier[] = [
        {
          name: "Lola Rosa's Catering",
          description: "Authentic Filipino cuisine for all occasions. Specializing in traditional lechon, pancit, and regional delicacies.",
          category: "Catering",
          location: "Tuguegarao, Cagayan",
          province: "Cagayan",
          phone: "+63 ************",
          email: "<EMAIL>",
          website: "https://lolarosacatering.com",
          imageUrl: "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300",
          startingPrice: 8000,
          verified: true,
          featured: true
        },
        {
          name: "Capture Moments Photography",
          description: "Award-winning wedding and event photography. Capturing your special moments with artistic flair and cultural sensitivity.",
          category: "Photography",
          location: "Ilagan, Isabela",
          province: "Isabela",
          phone: "+63 ************",
          email: "<EMAIL>",
          website: "https://capturemoments.com",
          imageUrl: "https://images.unsplash.com/photo-1511285560929-80b456fea0bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300",
          startingPrice: 15000,
          verified: true,
          featured: true
        },
        {
          name: "Fiesta Decorations",
          description: "Beautiful event decorations blending modern elegance with traditional Filipino charm. Complete setup and styling services.",
          category: "Decorations",
          location: "Bayombong, Nueva Vizcaya",
          province: "Nueva Vizcaya",
          phone: "+63 ************",
          email: "<EMAIL>",
          imageUrl: "https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300",
          startingPrice: 12000,
          verified: true,
          featured: true
        },
        {
          name: "Northern Lights Events",
          description: "Professional event planning and coordination services. Making your special day stress-free and memorable.",
          category: "Event Planning",
          location: "Santiago, Isabela",
          province: "Isabela",
          phone: "+63 ************",
          email: "<EMAIL>",
          imageUrl: "https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300",
          startingPrice: 25000,
          verified: true,
          featured: false
        },
        {
          name: "Melody Makers Entertainment",
          description: "Live bands, DJs, and cultural performers for all types of events. Traditional and modern entertainment options.",
          category: "Entertainment",
          location: "Cabarroguis, Quirino",
          province: "Quirino",
          phone: "+63 ************",
          email: "<EMAIL>",
          imageUrl: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300",
          startingPrice: 18000,
          verified: true,
          featured: false
        },
        {
          name: "Garden Paradise Venue",
          description: "Beautiful outdoor venue with mountain views. Perfect for weddings and special celebrations in the heart of Luzon.",
          category: "Venues",
          location: "Basco, Batanes",
          province: "Batanes",
          phone: "+63 ************",
          email: "<EMAIL>",
          imageUrl: "https://images.unsplash.com/photo-1519167758481-83f550bb49b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300",
          startingPrice: 30000,
          verified: true,
          featured: false
        }
      ];

      const insertedSuppliers = await db.insert(suppliers).values(
        suppliersData.map(supplier => ({
          ...supplier,
          rating: "0.0",
          reviewCount: 0
        }))
      ).returning();

      // Seed reviews
      const reviewsData: InsertReview[] = [
        {
          supplierId: insertedSuppliers[0].id,
          customerName: "Maria & Robert Cruz",
          customerLocation: "Tuguegarao, Cagayan",
          rating: 5,
          comment: "Lola Rosa's catering made our wedding day perfect! The food was absolutely delicious and authentic. Highly recommended!"
        },
        {
          supplierId: insertedSuppliers[1].id,
          customerName: "Jose Santos",
          customerLocation: "Isabela",
          rating: 5,
          comment: "Amazing photography service! They captured every special moment beautifully. Very professional and creative."
        },
        {
          supplierId: insertedSuppliers[2].id,
          customerName: "Ana Lopez",
          customerLocation: "Nueva Vizcaya",
          rating: 5,
          comment: "Fiesta Decorations transformed our venue into something magical. The attention to detail was incredible!"
        }
      ];

      await db.insert(reviews).values(reviewsData);

      // Update supplier ratings and review counts
      for (const supplier of insertedSuppliers) {
        const supplierReviews = await db.select().from(reviews).where(eq(reviews.supplierId, supplier.id));
        const avgRating = supplierReviews.length > 0
          ? (supplierReviews.reduce((sum, r) => sum + r.rating, 0) / supplierReviews.length).toFixed(1)
          : "0.0";

        await db.update(suppliers)
          .set({
            rating: avgRating,
            reviewCount: supplierReviews.length
          })
          .where(eq(suppliers.id, supplier.id));
      }

    } catch (error) {
      console.error("Error seeding database:", error);
    }
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
    return result[0];
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const result = await this.db.select().from(users).where(eq(users.email, email)).limit(1);
    return result[0];
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const result = await this.db.insert(users).values(insertUser).returning();
    return result[0];
  }

  async updateUserLastLogin(id: number): Promise<void> {
    await this.db.update(users)
      .set({ lastLoginAt: new Date() })
      .where(eq(users.id, id));
  }

  // Supplier methods
  async getAllSuppliers(): Promise<Supplier[]> {
    return await db.select().from(suppliers);
  }

  async getSupplier(id: number): Promise<Supplier | undefined> {
    const result = await db.select().from(suppliers).where(eq(suppliers.id, id)).limit(1);
    return result[0];
  }

  async getSuppliersByCategory(category: string): Promise<Supplier[]> {
    return await db.select().from(suppliers).where(eq(suppliers.category, category));
  }

  async getFeaturedSuppliers(): Promise<Supplier[]> {
    return await db.select().from(suppliers).where(eq(suppliers.featured, true));
  }

  async searchSuppliers(query: string, filters?: {
    category?: string;
    province?: string;
    minPrice?: number;
    maxPrice?: number;
  }): Promise<Supplier[]> {
    const conditions = [];

    if (query) {
      const lowerQuery = `%${query.toLowerCase()}%`;
      conditions.push(
        sql`(lower(${suppliers.name}) like ${lowerQuery} or
            lower(${suppliers.description}) like ${lowerQuery} or
            lower(${suppliers.category}) like ${lowerQuery} or
            lower(${suppliers.location}) like ${lowerQuery})`
      );
    }

    if (filters?.category) {
      conditions.push(eq(suppliers.category, filters.category));
    }

    if (filters?.province) {
      conditions.push(eq(suppliers.province, filters.province));
    }

    if (filters?.minPrice !== undefined) {
      conditions.push(gte(suppliers.startingPrice, filters.minPrice));
    }

    if (filters?.maxPrice !== undefined) {
      conditions.push(lte(suppliers.startingPrice, filters.maxPrice));
    }

    if (conditions.length > 0) {
      return await db.select().from(suppliers).where(and(...conditions));
    }

    return await db.select().from(suppliers);
  }

  async createSupplier(insertSupplier: InsertSupplier): Promise<Supplier> {
    const result = await db.insert(suppliers).values({
      ...insertSupplier,
      rating: "0.0",
      reviewCount: 0
    }).returning();
    return result[0];
  }

  // Review methods
  async getReviewsBySupplier(supplierId: number): Promise<Review[]> {
    return await db.select().from(reviews).where(eq(reviews.supplierId, supplierId));
  }

  async createReview(insertReview: InsertReview): Promise<Review> {
    const result = await db.insert(reviews).values(insertReview).returning();
    const review = result[0];

    // Update supplier review count and rating
    const supplierReviews = await this.getReviewsBySupplier(insertReview.supplierId);
    const avgRating = supplierReviews.reduce((sum, r) => sum + r.rating, 0) / supplierReviews.length;

    await db.update(suppliers)
      .set({
        rating: avgRating.toFixed(1),
        reviewCount: supplierReviews.length
      })
      .where(eq(suppliers.id, insertReview.supplierId));

    return review;
  }

  // Inquiry methods
  async getInquiriesBySupplier(supplierId: number): Promise<Inquiry[]> {
    return await db.select().from(inquiries).where(eq(inquiries.supplierId, supplierId));
  }

  async createInquiry(insertInquiry: InsertInquiry): Promise<Inquiry> {
    const result = await db.insert(inquiries).values(insertInquiry).returning();
    return result[0];
  }

  // Category methods
  async getAllCategories(): Promise<Category[]> {
    return await db.select().from(categories);
  }

  async getCategory(slug: string): Promise<Category | undefined> {
    const result = await db.select().from(categories).where(eq(categories.slug, slug)).limit(1);
    return result[0];
  }

  async createCategory(insertCategory: InsertCategory): Promise<Category> {
    const result = await db.insert(categories).values({
      ...insertCategory,
      supplierCount: 0
    }).returning();

    const category = result[0];

    // Update supplier count
    const categorySuppliers = await this.getSuppliersByCategory(category.name);
    await db.update(categories)
      .set({ supplierCount: categorySuppliers.length })
      .where(eq(categories.id, category.id));

    return category;
  }

  // Availability Calendar System methods
  async getSupplierAvailability(supplierId: number, startDate: Date, endDate: Date): Promise<AvailabilitySlot[]> {
    return await this.db.select().from(availabilitySlots)
      .where(
        and(
          eq(availabilitySlots.supplierId, supplierId),
          gte(availabilitySlots.date, startDate),
          lte(availabilitySlots.date, endDate)
        )
      )
      .orderBy(availabilitySlots.date, availabilitySlots.startTime);
  }

  async createAvailabilitySlot(slot: InsertAvailabilitySlot): Promise<AvailabilitySlot> {
    const result = await this.db.insert(availabilitySlots).values(slot).returning();
    return result[0];
  }

  async updateAvailabilitySlot(id: number, updates: Partial<InsertAvailabilitySlot>): Promise<void> {
    await this.db.update(availabilitySlots)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(availabilitySlots.id, id));
  }

  async deleteAvailabilitySlot(id: number): Promise<void> {
    await this.db.delete(availabilitySlots).where(eq(availabilitySlots.id, id));
  }

  // Booking methods
  async getSupplierBookings(supplierId: number, startDate?: Date, endDate?: Date): Promise<Booking[]> {
    if (startDate && endDate) {
      return await this.db.select().from(bookings)
        .where(
          and(
            eq(bookings.supplierId, supplierId),
            gte(bookings.bookingDate, startDate),
            lte(bookings.bookingDate, endDate)
          )
        )
        .orderBy(bookings.bookingDate, bookings.startTime);
    }

    return await this.db.select().from(bookings)
      .where(eq(bookings.supplierId, supplierId))
      .orderBy(bookings.bookingDate, bookings.startTime);
  }

  async getCustomerBookings(customerId: number): Promise<Booking[]> {
    return await this.db.select().from(bookings)
      .where(eq(bookings.customerId, customerId))
      .orderBy(bookings.bookingDate, bookings.startTime);
  }

  async createBooking(booking: InsertBooking): Promise<Booking> {
    const result = await this.db.insert(bookings).values(booking).returning();

    // Update availability slot current bookings count if associated
    if (booking.availabilitySlotId) {
      await this.db.execute(sql`
        UPDATE availability_slots
        SET current_bookings = current_bookings + 1
        WHERE id = ${booking.availabilitySlotId}
      `);
    }

    return result[0];
  }

  async updateBookingStatus(id: number, status: string, supplierNotes?: string): Promise<void> {
    const updates: any = { status, updatedAt: new Date() };
    if (supplierNotes) {
      updates.supplierNotes = supplierNotes;
    }

    await this.db.update(bookings)
      .set(updates)
      .where(eq(bookings.id, id));
  }

  // Supplier Settings methods
  async getSupplierSettings(supplierId: number): Promise<SupplierSettings | undefined> {
    const result = await this.db.select().from(supplierSettings)
      .where(eq(supplierSettings.supplierId, supplierId))
      .limit(1);
    return result[0];
  }

  async createOrUpdateSupplierSettings(settings: InsertSupplierSettings): Promise<SupplierSettings> {
    const existing = await this.getSupplierSettings(settings.supplierId);

    if (existing) {
      await this.db.update(supplierSettings)
        .set({ ...settings, updatedAt: new Date() })
        .where(eq(supplierSettings.supplierId, settings.supplierId));

      return await this.getSupplierSettings(settings.supplierId) as SupplierSettings;
    } else {
      const result = await this.db.insert(supplierSettings).values(settings).returning();
      return result[0];
    }
  }
}

export const storage = new DatabaseStorage();
