import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Textarea } from "@/components/ui/textarea";
import { 
  Sparkles, 
  MapPin, 
  Users, 
  DollarSign, 
  Calendar,
  Star,
  Award,
  TrendingUp,
  Heart,
  CheckCircle,
  Info,
  Target,
  Lightbulb,
  PiggyBank
} from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { PROVINCES, CATEGORIES } from "@/lib/types";
import type { Supplier } from "@shared/schema";

interface UserPreferences {
  eventType?: string;
  budget?: number;
  guestCount?: number;
  location?: string;
  province?: string;
  style?: string;
  priorities?: string[];
  dateFlexibility?: 'strict' | 'flexible' | 'very_flexible';
  specialRequirements?: string[];
}

interface RecommendationResult {
  supplier: Supplier;
  matchScore: number;
  reasons: string[];
  category: 'perfect_match' | 'great_option' | 'budget_friendly' | 'premium_choice';
  aiInsights: string;
}

interface SmartRecommendations {
  recommendations: RecommendationResult[];
  summary: string;
  budgetAnalysis: {
    totalEstimate: number;
    breakdown: Array<{ category: string; estimate: number; suppliers: string[] }>;
    savingsTips: string[];
  };
  alternatives: RecommendationResult[];
  eventPlanningTips: string[];
}

export default function SmartRecommendations() {
  const [preferences, setPreferences] = useState<UserPreferences>({
    budget: 50000,
    guestCount: 50,
    dateFlexibility: 'flexible'
  });
  const [showForm, setShowForm] = useState(true);
  const [recommendations, setRecommendations] = useState<SmartRecommendations | null>(null);

  const getRecommendationsMutation = useMutation({
    mutationFn: async (prefs: UserPreferences) => {
      const response = await apiRequest("POST", "/api/recommendations/smart", prefs);
      return response.json() as Promise<SmartRecommendations>;
    },
    onSuccess: (data) => {
      setRecommendations(data);
      setShowForm(false);
    }
  });

  const handleGetRecommendations = () => {
    getRecommendationsMutation.mutate(preferences);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'perfect_match': return <Target className="h-4 w-4" />;
      case 'great_option': return <Star className="h-4 w-4" />;
      case 'budget_friendly': return <PiggyBank className="h-4 w-4" />;
      case 'premium_choice': return <Award className="h-4 w-4" />;
      default: return <CheckCircle className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'perfect_match': return 'bg-filipino-orange text-white';
      case 'great_option': return 'bg-filipino-gold text-gray-900';
      case 'budget_friendly': return 'bg-filipino-green text-white';
      case 'premium_choice': return 'bg-purple-600 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const RecommendationCard = ({ result }: { result: RecommendationResult }) => (
    <Card className="hover:shadow-lg transition-shadow">
      <CardContent className="p-6">
        <div className="flex gap-4">
          <img 
            src={result.supplier.imageUrl} 
            alt={result.supplier.name}
            className="w-20 h-20 rounded-lg object-cover"
          />
          <div className="flex-1">
            <div className="flex items-start justify-between mb-2">
              <div>
                <h3 className="font-bold text-lg text-gray-900">{result.supplier.name}</h3>
                <p className="text-sm text-gray-600">{result.supplier.category}</p>
              </div>
              <div className="flex items-center gap-2">
                <Badge className={getCategoryColor(result.category)}>
                  {getCategoryIcon(result.category)}
                  <span className="ml-1 capitalize">{result.category.replace('_', ' ')}</span>
                </Badge>
                <div className="text-right">
                  <div className="text-lg font-bold text-filipino-orange">
                    {result.matchScore}%
                  </div>
                  <div className="text-xs text-gray-500">Match</div>
                </div>
              </div>
            </div>
            
            <p className="text-sm text-gray-700 mb-3">
              {result.supplier.description.substring(0, 120)}...
            </p>
            
            <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
              <span className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                {result.supplier.location}
              </span>
              <span className="flex items-center gap-1">
                <Star className="h-3 w-3 text-yellow-500" />
                {result.supplier.rating}/5
              </span>
              <span className="text-filipino-orange font-medium">
                From ₱{result.supplier.startingPrice.toLocaleString()}
              </span>
            </div>
            
            <div className="mb-3">
              <p className="text-sm text-blue-700 font-medium mb-1">
                <Info className="h-3 w-3 inline mr-1" />
                AI Insights:
              </p>
              <p className="text-sm text-gray-600">{result.aiInsights}</p>
            </div>
            
            <div className="mb-4">
              <p className="text-xs text-gray-500 mb-2">Why this matches you:</p>
              <div className="space-y-1">
                {result.reasons.slice(0, 3).map((reason, idx) => (
                  <div key={idx} className="flex items-center gap-2 text-xs text-gray-600">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    {reason}
                  </div>
                ))}
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button size="sm" className="bg-filipino-orange hover:bg-orange-600">
                Contact Supplier
              </Button>
              <Button size="sm" variant="outline">
                View Profile
              </Button>
              <Button size="sm" variant="outline">
                <Heart className="h-3 w-3 mr-1" />
                Save
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (!showForm && recommendations) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h2 className="font-poppins font-bold text-3xl text-gray-900 mb-2">
            <Sparkles className="h-8 w-8 text-filipino-orange inline mr-2" />
            Your Smart Recommendations
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">{recommendations.summary}</p>
          <Button 
            variant="outline" 
            onClick={() => setShowForm(true)}
            className="mt-4"
          >
            Adjust Preferences
          </Button>
        </div>

        {/* Budget Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-filipino-orange" />
              Budget Analysis & Tips
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="text-center p-4 bg-filipino-orange/10 rounded-lg mb-4">
                  <div className="text-2xl font-bold text-filipino-orange">
                    ₱{recommendations.budgetAnalysis.totalEstimate.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Estimated Total Cost</div>
                </div>
                
                {recommendations.budgetAnalysis.breakdown.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">Cost Breakdown:</h4>
                    <div className="space-y-2">
                      {recommendations.budgetAnalysis.breakdown.map((item, idx) => (
                        <div key={idx} className="flex justify-between text-sm">
                          <span>{item.category}</span>
                          <span className="font-medium">₱{item.estimate.toLocaleString()}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Lightbulb className="h-4 w-4 text-yellow-500" />
                  Money-Saving Tips:
                </h4>
                <div className="space-y-2">
                  {recommendations.budgetAnalysis.savingsTips.map((tip, idx) => (
                    <div key={idx} className="flex items-start gap-2 text-sm text-gray-600">
                      <PiggyBank className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                      {tip}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Recommendations */}
        <div>
          <h3 className="font-semibold text-xl mb-4">Perfect Matches for You</h3>
          <div className="space-y-4">
            {recommendations.recommendations.map((result, idx) => (
              <RecommendationCard key={idx} result={result} />
            ))}
          </div>
        </div>

        {/* Alternatives */}
        {recommendations.alternatives.length > 0 && (
          <div>
            <h3 className="font-semibold text-xl mb-4">Alternative Options</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {recommendations.alternatives.map((result, idx) => (
                <RecommendationCard key={idx} result={result} />
              ))}
            </div>
          </div>
        )}

        {/* Event Planning Tips */}
        {recommendations.eventPlanningTips.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-filipino-gold" />
                Event Planning Tips
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {recommendations.eventPlanningTips.map((tip, idx) => (
                  <div key={idx} className="flex items-start gap-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-filipino-green mt-0.5 flex-shrink-0" />
                    {tip}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-center">
          <Sparkles className="h-6 w-6 text-filipino-orange" />
          Get Smart AI Recommendations
        </CardTitle>
        <p className="text-center text-gray-600">
          Tell us about your event and we'll find the perfect suppliers for you!
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Event Type</label>
              <Select onValueChange={(value) => setPreferences(prev => ({ ...prev, eventType: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select event type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="wedding">Wedding</SelectItem>
                  <SelectItem value="birthday">Birthday Party</SelectItem>
                  <SelectItem value="corporate">Corporate Event</SelectItem>
                  <SelectItem value="anniversary">Anniversary</SelectItem>
                  <SelectItem value="graduation">Graduation</SelectItem>
                  <SelectItem value="baptism">Baptism/Christening</SelectItem>
                  <SelectItem value="reunion">Family Reunion</SelectItem>
                  <SelectItem value="conference">Conference/Seminar</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Province</label>
              <Select onValueChange={(value) => setPreferences(prev => ({ ...prev, province: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select province" />
                </SelectTrigger>
                <SelectContent>
                  {PROVINCES.map(province => (
                    <SelectItem key={province} value={province}>{province}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Specific Location (Optional)</label>
              <Input
                placeholder="City or specific area"
                value={preferences.location || ''}
                onChange={(e) => setPreferences(prev => ({ ...prev, location: e.target.value }))}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Event Style (Optional)</label>
              <Input
                placeholder="e.g., Traditional, Modern, Garden, Beach"
                value={preferences.style || ''}
                onChange={(e) => setPreferences(prev => ({ ...prev, style: e.target.value }))}
              />
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Budget: ₱{preferences.budget?.toLocaleString() || '50,000'}
              </label>
              <Slider
                value={[preferences.budget || 50000]}
                onValueChange={([value]) => setPreferences(prev => ({ ...prev, budget: value }))}
                max={200000}
                min={10000}
                step={5000}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>₱10,000</span>
                <span>₱200,000+</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Guest Count: {preferences.guestCount || 50} people
              </label>
              <Slider
                value={[preferences.guestCount || 50]}
                onValueChange={([value]) => setPreferences(prev => ({ ...prev, guestCount: value }))}
                max={500}
                min={10}
                step={10}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>10 people</span>
                <span>500+ people</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Date Flexibility</label>
              <Select 
                defaultValue="flexible"
                onValueChange={(value: any) => setPreferences(prev => ({ ...prev, dateFlexibility: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="strict">Fixed Date Only</SelectItem>
                  <SelectItem value="flexible">Some Flexibility</SelectItem>
                  <SelectItem value="very_flexible">Very Flexible</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Special Requirements (Optional)</label>
              <Textarea
                placeholder="Any special needs, dietary restrictions, accessibility requirements, etc."
                value={preferences.specialRequirements?.join(', ') || ''}
                onChange={(e) => setPreferences(prev => ({ 
                  ...prev, 
                  specialRequirements: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                }))}
                rows={3}
              />
            </div>
          </div>
        </div>

        <Button
          onClick={handleGetRecommendations}
          disabled={getRecommendationsMutation.isPending}
          className="w-full bg-filipino-orange hover:bg-orange-600 text-white py-3 text-lg"
        >
          {getRecommendationsMutation.isPending ? (
            <>
              <Sparkles className="h-5 w-5 mr-2 animate-spin" />
              AI is analyzing your preferences...
            </>
          ) : (
            <>
              <Sparkles className="h-5 w-5 mr-2" />
              Get My Smart Recommendations
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}