import { useQuery } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { useLocation, useSearch } from "wouter";
import Navbar from "@/components/navbar";
import SearchBar from "@/components/search-bar";
import SupplierCard from "@/components/supplier-card";
import ContactForm from "@/components/contact-form";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Filter, SlidersHorizontal } from "lucide-react";
import type { Supplier } from "@shared/schema";
import { PROVINCES, CATEGORIES, type SearchFilters } from "@/lib/types";

export default function Suppliers() {
  const [, setLocation] = useLocation();
  const search = useSearch();
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  
  // Parse URL parameters
  const urlParams = new URLSearchParams(search);
  const initialQuery = urlParams.get("q") || "";
  const initialCategory = urlParams.get("category") || "";
  const initialProvince = urlParams.get("province") || "";
  const initialMinPrice = urlParams.get("minPrice") ? parseInt(urlParams.get("minPrice")!) : 0;
  const initialMaxPrice = urlParams.get("maxPrice") ? parseInt(urlParams.get("maxPrice")!) : 100000;

  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [filters, setFilters] = useState<SearchFilters>({
    category: initialCategory,
    province: initialProvince,
    minPrice: initialMinPrice,
    maxPrice: initialMaxPrice,
  });

  // Build query string for API call
  const buildQueryString = () => {
    const params = new URLSearchParams();
    if (searchQuery) params.set("q", searchQuery);
    if (filters.category) params.set("category", filters.category);
    if (filters.province) params.set("province", filters.province);
    if (filters.minPrice && filters.minPrice > 0) params.set("minPrice", filters.minPrice.toString());
    if (filters.maxPrice && filters.maxPrice < 100000) params.set("maxPrice", filters.maxPrice.toString());
    return params.toString();
  };

  const { data: suppliers = [], isLoading, error } = useQuery<Supplier[]>({
    queryKey: [`/api/suppliers/search?${buildQueryString()}`],
  });

  const handleSearch = (query: string, province?: string) => {
    setSearchQuery(query);
    if (province) {
      setFilters(prev => ({ ...prev, province }));
    }
    updateURL(query, { ...filters, province: province || filters.province });
  };

  const updateURL = (query: string, newFilters: SearchFilters) => {
    const params = new URLSearchParams();
    if (query) params.set("q", query);
    if (newFilters.category) params.set("category", newFilters.category);
    if (newFilters.province) params.set("province", newFilters.province);
    if (newFilters.minPrice && newFilters.minPrice > 0) params.set("minPrice", newFilters.minPrice.toString());
    if (newFilters.maxPrice && newFilters.maxPrice < 100000) params.set("maxPrice", newFilters.maxPrice.toString());
    
    setLocation(`/suppliers?${params.toString()}`);
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    const cleanValue = value === "all" ? "" : value;
    const newFilters = { ...filters, [key]: cleanValue };
    setFilters(newFilters);
    updateURL(searchQuery, newFilters);
  };

  const clearFilters = () => {
    const clearedFilters = { category: "", province: "", minPrice: 0, maxPrice: 100000 };
    setFilters(clearedFilters);
    updateURL(searchQuery, clearedFilters);
  };

  const activeFilterCount = Object.values(filters).filter(value => 
    value && value !== "" && value !== 0 && value !== 100000
  ).length;

  const handleContact = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
  };

  return (
    <div className="min-h-screen bg-warm-gray">
      <Navbar />
      
      {/* Header */}
      <section className="bg-white border-b border-gray-200 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="font-poppins font-bold text-3xl text-gray-900 mb-6">
            Find Event Suppliers
          </h1>
          <SearchBar 
            onSearch={handleSearch}
            defaultQuery={searchQuery}
            defaultProvince={filters.province}
          />
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="lg:w-80">
            <Card className="sticky top-4">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <SlidersHorizontal className="h-5 w-5" />
                    Filters
                    {activeFilterCount > 0 && (
                      <Badge variant="secondary">{activeFilterCount}</Badge>
                    )}
                  </CardTitle>
                  {activeFilterCount > 0 && (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={clearFilters}
                      className="text-filipino-orange hover:text-orange-600"
                    >
                      Clear all
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Category Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Service Category
                  </label>
                  <Select 
                    value={filters.category || ""} 
                    onValueChange={(value) => handleFilterChange("category", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {CATEGORIES.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Province Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Province
                  </label>
                  <Select 
                    value={filters.province || ""} 
                    onValueChange={(value) => handleFilterChange("province", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Provinces" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Provinces</SelectItem>
                      {PROVINCES.map((province) => (
                        <SelectItem key={province} value={province}>
                          {province}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Price Range Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Price Range
                  </label>
                  <div className="px-2">
                    <Slider
                      value={[filters.minPrice || 0, filters.maxPrice || 100000]}
                      onValueChange={([min, max]) => {
                        handleFilterChange("minPrice", min);
                        handleFilterChange("maxPrice", max);
                      }}
                      max={100000}
                      min={0}
                      step={1000}
                      className="mb-2"
                    />
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>₱{(filters.minPrice || 0).toLocaleString()}</span>
                      <span>₱{(filters.maxPrice || 100000).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Results */}
          <div className="flex-1">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {isLoading ? "Searching..." : `${suppliers.length} suppliers found`}
                </h2>
                {searchQuery && (
                  <p className="text-gray-600">Results for "{searchQuery}"</p>
                )}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="lg:hidden"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
              </Button>
            </div>

            {/* Active Filters */}
            {activeFilterCount > 0 && (
              <div className="flex flex-wrap gap-2 mb-6">
                {filters.category && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    {filters.category}
                    <button 
                      onClick={() => handleFilterChange("category", "")}
                      className="ml-1 hover:text-red-600"
                    >
                      ×
                    </button>
                  </Badge>
                )}
                {filters.province && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    {filters.province}
                    <button 
                      onClick={() => handleFilterChange("province", "")}
                      className="ml-1 hover:text-red-600"
                    >
                      ×
                    </button>
                  </Badge>
                )}
                {(filters.minPrice && filters.minPrice > 0) || (filters.maxPrice && filters.maxPrice < 100000) ? (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    ₱{(filters.minPrice || 0).toLocaleString()} - ₱{(filters.maxPrice || 100000).toLocaleString()}
                    <button 
                      onClick={() => {
                        handleFilterChange("minPrice", 0);
                        handleFilterChange("maxPrice", 100000);
                      }}
                      className="ml-1 hover:text-red-600"
                    >
                      ×
                    </button>
                  </Badge>
                ) : null}
              </div>
            )}

            {/* Loading State */}
            {isLoading && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="bg-gray-300 rounded-2xl h-64 mb-4"></div>
                    <div className="bg-gray-300 h-4 rounded mb-2"></div>
                    <div className="bg-gray-300 h-3 rounded mb-4"></div>
                  </div>
                ))}
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="text-center py-12">
                <p className="text-red-600 mb-4">Failed to load suppliers. Please try again.</p>
                <Button onClick={() => window.location.reload()}>Retry</Button>
              </div>
            )}

            {/* Results */}
            {!isLoading && !error && (
              <>
                {suppliers.length === 0 ? (
                  <div className="text-center py-12">
                    <p className="text-gray-600 mb-4">No suppliers found matching your criteria.</p>
                    <Button onClick={clearFilters} variant="outline">
                      Clear filters
                    </Button>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                    {suppliers.map((supplier) => (
                      <SupplierCard
                        key={supplier.id}
                        supplier={supplier}
                        onContact={handleContact}
                      />
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Contact Form Modal */}
      {selectedSupplier && (
        <ContactForm
          supplier={selectedSupplier}
          isOpen={!!selectedSupplier}
          onClose={() => setSelectedSupplier(null)}
        />
      )}
    </div>
  );
}
