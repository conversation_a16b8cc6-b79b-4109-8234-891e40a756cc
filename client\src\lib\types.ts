export interface SearchFilters {
  category?: string;
  province?: string;
  minPrice?: number;
  maxPrice?: number;
}

export interface SearchParams {
  q?: string;
  filters?: SearchFilters;
}

export const PROVINCES = [
  "Cagayan",
  "Isabela", 
  "Nueva Vizcaya",
  "<PERSON><PERSON><PERSON>",
  "Batanes"
] as const;

export const CATEGORIES = [
  // Event Services
  "Wedding Services",
  "Catering",
  "Photography", 
  "Event Planning",
  "Decorations",
  "Entertainment",
  "Venues",
  "Transportation",
  
  // Home & Construction Services
  "Electrician",
  "Plumber",
  "Carpenter",
  "Woodworking",
  "Painter",
  "Mason & Construction",
  "Roofing",
  "Flooring",
  "HVAC & Aircon",
  "Landscaping & Gardening",
  "Interior Design",
  "Architecture",
  
  // Personal Services
  "Cook & Chef",
  "Housekeeping & Cleaning",
  "Laundry Services",
  "Childcare & Nanny",
  "Elderly Care",
  "Pet Care & Grooming",
  "Tutoring & Education",
  "Fitness & Personal Training",
  "Beauty & Wellness",
  "Massage Therapy",
  
  // Business Services
  "Accounting & Bookkeeping",
  "Legal Services",
  "IT & Computer Repair",
  "Web Design & Development",
  "Marketing & Advertising",
  "Graphic Design",
  "Translation Services",
  "Business Consulting",
  
  // Specialized Services
  "Auto Repair & Mechanics",
  "Appliance Repair",
  "Solar Installation",
  "Pest Control",
  "Delivery & Logistics",
  "Real Estate",
  "Insurance Services",
  "Financial Advisory"
] as const;

export type Province = typeof PROVINCES[number];
export type CategoryType = typeof CATEGORIES[number];
