import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Star, MessageCircle, ThumbsUp, Send } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import type { Supplier } from "@shared/schema";

interface FeedbackSystemProps {
  supplier: Supplier;
  isOpen: boolean;
  onClose: () => void;
}

const FEEDBACK_PROMPTS = {
  "Catering": {
    title: "How was your catering experience?",
    prompt: "Did you enjoy your meal from {supplierName}? Your comments on taste, service, and presentation help others plan the perfect event.",
    categories: ["Food Quality", "Service", "Presentation", "Punctuality", "Value"]
  },
  "Photography": {
    title: "How did your photo session go?",
    prompt: "How did your shoot go with {supplierName}? Let us know how they captured your special moments!",
    categories: ["Creativity", "Professionalism", "Photo Quality", "Communication", "Delivery Time"]
  },
  "Videography": {
    title: "How was your videography service?",
    prompt: "How did your shoot go with {supplierName}? Let us know how they captured your special moments!",
    categories: ["Video Quality", "Creativity", "Professionalism", "Communication", "Delivery Time"]
  },
  "Sound & Lighting": {
    title: "How was the sound and setup?",
    prompt: "How was the sound and stage setup by {supplierName} at your event? Your feedback keeps our listings reliable.",
    categories: ["Equipment Quality", "Setup Time", "Technical Support", "Sound Quality", "Reliability"]
  },
  "Flowers & Decoration": {
    title: "How was your event decoration?",
    prompt: "Was the setup everything you dreamed of? Share how {supplierName} helped bring your event to life!",
    categories: ["Design Creativity", "Setup Quality", "Timeline", "Theme Execution", "Value"]
  },
  "Beauty & Wellness": {
    title: "How was your beauty session?",
    prompt: "How was your glam session with {supplierName}? Your honest review helps others feel confident booking.",
    categories: ["Skill Level", "Comfort", "Style Options", "Professionalism", "Results"]
  },
  "Electrician": {
    title: "How was the electrical work?",
    prompt: "Did {supplierName} get the job done? Tell us about the service quality and outcome of your electrical work.",
    categories: ["Work Quality", "Timeliness", "Pricing", "Professionalism", "Problem Solving"]
  },
  "Plumber": {
    title: "How was the plumbing service?",
    prompt: "Did {supplierName} get the job done? Tell us about the service quality and outcome of your plumbing request.",
    categories: ["Work Quality", "Response Time", "Pricing", "Cleanliness", "Problem Resolution"]
  },
  "Carpenter": {
    title: "How was the carpentry work?",
    prompt: "Did {supplierName} get the job done? Tell us about the service quality and outcome of your carpentry project.",
    categories: ["Craftsmanship", "Attention to Detail", "Timeline", "Communication", "Value"]
  },
  "Housekeeping & Cleaning": {
    title: "How was the cleaning service?",
    prompt: "How was your cleaning service with {supplierName}? Your review helps others find reliable cleaning professionals.",
    categories: ["Thoroughness", "Reliability", "Trustworthiness", "Efficiency", "Value"]
  },
  "Tutoring & Education": {
    title: "How was the tutoring experience?",
    prompt: "How was your learning experience with {supplierName}? Your feedback helps other students and parents choose the right tutor.",
    categories: ["Teaching Quality", "Patience", "Communication", "Progress", "Reliability"]
  },
  "Web Design & Development": {
    title: "How was the web development project?",
    prompt: "How did your project go with {supplierName}? Your review helps others find skilled web developers.",
    categories: ["Technical Skills", "Communication", "Timeline", "Creativity", "Support"]
  }
};

export default function FeedbackSystem({ supplier, isOpen, onClose }: FeedbackSystemProps) {
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [feedback, setFeedback] = useState("");
  const [category, setCategory] = useState("");
  
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const feedbackConfig = FEEDBACK_PROMPTS[supplier.category as keyof typeof FEEDBACK_PROMPTS] || {
    title: "How was your experience?",
    prompt: `Thanks for using Hello Luzon! How was your experience with ${supplier.name}? We'd love your feedback — it helps others make informed choices.`,
    categories: ["Service Quality", "Communication", "Professionalism", "Value", "Reliability"]
  };

  const submitFeedbackMutation = useMutation({
    mutationFn: async (reviewData: any) => {
      const response = await apiRequest('/api/reviews', {
        method: 'POST',
        body: JSON.stringify(reviewData)
      });
      return response;
    },
    onSuccess: () => {
      toast({
        title: "Thank you for your feedback! 🙏",
        description: "Your review helps others find great service providers.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/suppliers', supplier.id] });
      onClose();
      setRating(0);
      setFeedback("");
      setCategory("");
    },
    onError: () => {
      toast({
        title: "Submission Error",
        description: "Couldn't submit your review. Please try again.",
        variant: "destructive"
      });
    }
  });

  const handleSubmit = () => {
    if (rating === 0 || !feedback.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide a rating and feedback comment.",
        variant: "destructive"
      });
      return;
    }

    submitFeedbackMutation.mutate({
      supplierId: supplier.id,
      rating,
      comment: feedback,
      category: category || "General",
      reviewerName: "Anonymous Customer" // In a real app, you'd get this from user auth
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="w-5 h-5 text-filipino-orange" />
            {feedbackConfig.title}
          </CardTitle>
          <CardDescription>
            {feedbackConfig.prompt.replace('{supplierName}', supplier.name)}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Star Rating */}
          <div>
            <Label className="text-base font-medium mb-2 block">Overall Rating</Label>
            <div className="flex items-center gap-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  className="p-1 transition-colors"
                >
                  <Star 
                    className={`w-8 h-8 ${
                      (hoveredRating || rating) >= star 
                        ? 'fill-yellow-400 text-yellow-400' 
                        : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
              <span className="ml-2 text-sm text-gray-600">
                {rating > 0 && `${rating} star${rating !== 1 ? 's' : ''}`}
              </span>
            </div>
          </div>

          {/* Category Selection */}
          <div>
            <Label htmlFor="category" className="text-base font-medium">
              What aspect would you like to highlight? (Optional)
            </Label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {feedbackConfig.categories.map((cat) => (
                  <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Feedback Text */}
          <div>
            <Label htmlFor="feedback" className="text-base font-medium">
              Share your experience
            </Label>
            <Textarea
              id="feedback"
              placeholder="Tell us about your experience - what went well? Any suggestions for improvement?"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              rows={4}
              className="mt-2"
            />
            <p className="text-sm text-gray-500 mt-1">
              Your feedback helps other customers and helps {supplier.name} improve their service.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button 
              variant="outline" 
              onClick={onClose}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSubmit}
              disabled={submitFeedbackMutation.isPending || rating === 0 || !feedback.trim()}
              className="flex-1 bg-filipino-orange hover:bg-orange-600"
            >
              {submitFeedbackMutation.isPending ? (
                "Submitting..."
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Submit Review
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}