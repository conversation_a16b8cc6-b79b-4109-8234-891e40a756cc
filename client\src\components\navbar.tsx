import { useState } from "react";
import { Link, useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { MapPin, Menu, X, Store, User, LogOut } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import AuthModal from "@/components/auth-modal";

export default function Navbar() {
  const [location] = useLocation();
  const [isOpen, setIsOpen] = useState(false);
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const [authUserType, setAuthUserType] = useState<'customer' | 'supplier'>('customer');

  // Check if user is logged in
  const { data: authData } = useQuery({
    queryKey: ['/api/auth/me'],
    retry: false
  });

  const navigation = [
    { name: "Browse", href: "/suppliers" },
    { name: "Recommendations", href: "/recommendations" },
    { name: "Pricing", href: "/pricing" },
    { name: "Admin", href: "/admin" },
  ];

  return (
    <div className="sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <nav className="mx-auto flex h-16 max-w-7xl items-center justify-between px-4 sm:px-6 lg:px-8">
        <div className="flex items-center">
          <Link href="/">
            <div className="flex items-center space-x-3">
              <div className="relative h-10 w-16">
                <svg viewBox="0 0 100 100" className="w-full h-full">
                  <defs>
                    <linearGradient id="sunGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#FCD34D" />
                      <stop offset="50%" stopColor="#F59E0B" />
                      <stop offset="100%" stopColor="#F97316" />
                    </linearGradient>
                    <linearGradient id="mountainGrad" x1="0%" y1="100%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#059669" />
                      <stop offset="50%" stopColor="#10B981" />
                      <stop offset="100%" stopColor="#34D399" />
                    </linearGradient>
                    <linearGradient id="waterGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#0891B2" />
                      <stop offset="100%" stopColor="#06B6D4" />
                    </linearGradient>
                  </defs>
                  
                  {/* Water base */}
                  <ellipse cx="50" cy="85" rx="45" ry="12" fill="url(#waterGrad)" opacity="0.8"/>
                  <ellipse cx="50" cy="82" rx="40" ry="8" fill="url(#waterGrad)" opacity="0.6"/>
                  
                  {/* Mountains */}
                  <path d="M5 75 L25 45 L35 55 L45 40 L55 50 L65 35 L75 45 L85 40 L95 50 L100 75 Z" fill="url(#mountainGrad)"/>
                  <path d="M10 75 L30 50 L40 60 L50 45 L60 55 L70 40 L80 50 L90 45 L95 75 Z" fill="url(#mountainGrad)" opacity="0.8"/>
                  
                  {/* Sun */}
                  <circle cx="75" cy="25" r="12" fill="url(#sunGrad)"/>
                  <g stroke="#FCD34D" strokeWidth="2" opacity="0.7">
                    <line x1="75" y1="8" x2="75" y2="12"/>
                    <line x1="92" y1="25" x2="88" y2="25"/>
                    <line x1="75" y1="42" x2="75" y2="38"/>
                    <line x1="58" y1="25" x2="62" y2="25"/>
                    <line x1="86" y1="14" x2="84" y2="16"/>
                    <line x1="84" y1="34" x2="86" y2="36"/>
                    <line x1="64" y1="36" x2="66" y2="34"/>
                    <line x1="66" y1="16" x2="64" y2="14"/>
                  </g>
                  
                  {/* Clouds */}
                  <circle cx="25" cy="20" r="4" fill="#F3F4F6" opacity="0.8"/>
                  <circle cx="30" cy="18" r="5" fill="#F3F4F6" opacity="0.8"/>
                  <circle cx="35" cy="20" r="4" fill="#F3F4F6" opacity="0.8"/>
                  
                  {/* Hello Luzon text */}
                  <text x="50" y="70" textAnchor="middle" fontSize="8" fontWeight="bold" fill="#FFFFFF" fontFamily="serif">Hello</text>
                  <text x="50" y="78" textAnchor="middle" fontSize="10" fontWeight="bold" fill="#FFFFFF" fontFamily="serif" fontStyle="italic">Luzon</text>
                </svg>
              </div>
              <span className="text-xl font-bold text-filipino-orange">Hello Luzon!</span>
            </div>
          </Link>
        </div>

        <div className="hidden lg:flex lg:items-center lg:space-x-8">
          <div className="flex space-x-8">
            {navigation.map((item) => (
              <Link key={item.name} href={item.href}>
                <span
                  className={`text-sm font-medium transition-colors hover:text-filipino-orange ${
                    location === item.href ? "text-filipino-orange" : "text-gray-700"
                  }`}
                >
                  {item.name}
                </span>
              </Link>
            ))}
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {authData?.user ? (
            // Logged in user menu
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  <span className="hidden md:inline">{authData.user.firstName} {authData.user.lastName}</span>
                  <span className="md:hidden">Profile</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuItem>
                  <User className="w-4 h-4 mr-2" />
                  My Profile
                </DropdownMenuItem>
                {authData.user.userType === 'supplier' && (
                  <DropdownMenuItem>
                    <Store className="w-4 h-4 mr-2" />
                    My Business
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600">
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            // Not logged in - show auth buttons
            <>
              <Button 
                variant="ghost" 
                className="text-gray-700 hover:text-filipino-orange hidden md:flex"
                onClick={() => {
                  setAuthMode('register');
                  setAuthUserType('supplier');
                  setAuthModalOpen(true);
                }}
              >
                <Store className="h-4 w-4 mr-2" />
                Join as Service Provider
              </Button>
              
              <Button 
                className="bg-filipino-orange hover:bg-orange-600 text-white"
                onClick={() => {
                  setAuthMode('login');
                  setAuthUserType('customer');
                  setAuthModalOpen(true);
                }}
              >
                <User className="h-4 w-4 mr-2 md:hidden" />
                <span className="hidden md:inline">Sign In</span>
              </Button>
            </>
          )}

          {/* Mobile menu */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <div className="flex flex-col space-y-4 mt-6">
                {navigation.map((item) => (
                  <Link key={item.name} href={item.href}>
                    <span 
                      className={`block py-2 text-lg font-medium transition-colors hover:text-filipino-orange ${
                        location === item.href ? "text-filipino-orange" : "text-gray-700"
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      {item.name}
                    </span>
                  </Link>
                ))}
                <Button 
                  variant="ghost" 
                  className="justify-start text-gray-700 hover:text-filipino-orange" 
                  onClick={() => {
                    setAuthMode('register');
                    setAuthUserType('supplier');
                    setAuthModalOpen(true);
                    setIsOpen(false);
                  }}
                >
                  <Store className="h-4 w-4 mr-2" />
                  Join as Service Provider
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </nav>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        defaultTab={authMode}
        userType={authUserType}
      />
    </div>
  );
}