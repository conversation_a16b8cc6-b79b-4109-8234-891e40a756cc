I need a fully functional web application built on Replit to manage fidelity bond documents for employees. The app must allow users to upload bond documents, search them by various criteria, and receive automated email notifications when bonds are nearing expiration. The application should be secure, user-friendly, and deployable directly on Replit. Below are the detailed requirements:

Functional Requirements:
Document Upload and Storage:
Users can upload fidelity bond documents in common formats (PDF, DOCX, PNG, JPEG, max 5MB per file).
Each document must store metadata:
Employee ID (unique alphanumeric, e.g., EMP123).
Employee Name (text).
Bond Number (unique alphanumeric).
Bond Issue Date (date).
Bond Expiry Date (date, mandatory for expiration tracking).
Optional: Notes (text, max 500 characters).
Store metadata in a SQLite database (built into Replit’s Python environment).
Store uploaded files in Replit’s file system (e.g., /uploads directory).
Validate file types and sizes, displaying clear error messages for invalid uploads.
Search Functionality:
Provide a search interface to query documents by:
Employee ID (exact or partial match).
Employee Name (partial match, case-insensitive).
Bond Number (exact or partial match).
Date Range (issue or expiry date).
Display search results in a table with columns: Employee ID, Employee Name, Bond Number, Issue Date, Expiry Date, and a “Download” link/button.
Ensure searches are fast (return results in <2 seconds for up to 1,000 documents).
Expiration Email Notifications:
Automatically check for bonds expiring within a configurable period (default: 30 days).
Send email notifications to an admin email (configurable via a settings page) when a bond is nearing expiration.
Email content must include:
Employee Name, Employee ID, Bond Number, Expiry Date.
A link to the app’s document view page (e.g., https://<replit-app-url>/document/<id>).
Use a Python library like smtplib with a free SMTP service (e.g., Gmail’s SMTP with an App Password) for email sending.
Implement a daily cron-like task (e.g., using schedule library or Replit’s background tasks) to check for expiring bonds.
User Interface:
Create a responsive web UI using HTML, CSS (preferably Tailwind CSS via CDN), and JavaScript.
Include the following pages:
Home/Dashboard: Show total documents, bonds expiring soon (within 30 days), and links to upload/search.
Upload Page: Form for metadata input and file upload.
Search Page: Input fields for search criteria and a results table.
Settings Page: Configure admin email and notification period.
Document View Page: Display metadata and a download link for a specific document.
Use clear error messages (e.g., “Invalid file format” or “Missing required field”).
Ensure mobile-friendly design (responsive layouts via Tailwind CSS).
Security:
Implement basic authentication (username/password) using Flask’s session management.
Store passwords securely (hashed with bcrypt).
Use HTTPS (Replit provides this by default for deployed apps).
Validate all inputs to prevent SQL injection and XSS attacks.
Restrict file uploads to safe formats and scan for malicious content if possible.
Ensure uploaded files are stored with unique names (e.g., using UUIDs) to avoid overwrites.
Additional Features:
Allow admin users to delete documents with a confirmation prompt.
Provide an option to export search results as a CSV file (columns: Employee ID, Name, Bond Number, Issue Date, Expiry Date).
Display a confirmation message after successful uploads or updates.
Technical Requirements:
Tech Stack:
Backend: Python with Flask (available in Replit’s Python template).
Database: SQLite for metadata storage (built into Python).
File Storage: Replit’s file system (/uploads directory).
Frontend: HTML, JavaScript, and Tailwind CSS (via CDN: https://cdn.tailwindcss.com).
Email: Use smtplib with Gmail SMTP (user provides credentials via settings).
Dependencies: Install via Replit’s requirements.txt (e.g., flask, python-dotenv, bcrypt, schedule).
Replit-Specific:
Use Replit’s built-in Python environment.
Configure the app to run on 0.0.0.0:8080 (Replit’s default).
Store sensitive data (e.g., SMTP credentials) in Replit’s Secrets (environment variables).
Ensure the app is deployable via Replit’s web hosting (autogenerated URL).
Code Quality:
Write modular code with comments explaining key functions.
Handle errors gracefully (e.g., file upload failures, database errors).
Include a README.md with setup, usage, and deployment instructions.
Deliverables:
Complete source code for the Flask web app, including:
app.py (Flask backend with routes for upload, search, settings, and document view).
templates/ (HTML files for UI).
static/ (CSS/JavaScript if needed, beyond Tailwind CDN).
uploads/ (directory for file storage).
database.db (SQLite database for metadata).
requirements.txt (list dependencies like flask, bcrypt, schedule).
A sample email template for expiration notifications.
Instructions for setting up and running the app on Replit.
A deployed Replit app URL (or instructions to deploy).
Constraints:
Handle up to 1,000 documents efficiently (quick searches, minimal storage overhead).
Keep the app lightweight to run smoothly on Replit’s free tier.
Avoid external cloud storage (e.g., AWS S3) to simplify Replit deployment; use local file system.
Ensure email setup is user-configurable (no hard-coded credentials).
Example Workflow:
User logs in with a username/password.
User navigates to the upload page, enters metadata (Employee ID, Name, Bond Number, Dates), and uploads a PDF.
The app saves the file to /uploads and metadata to SQLite, showing a success message.
User searches by Employee Name, sees a table of results, and downloads a document.
Daily, the app checks for bonds expiring within 30 days and sends an email to the admin with details and a link.
Sample Email Template:
text

Copy
Subject: Fidelity Bond Expiration Notification

Dear Administrator,

The fidelity bond for the following employee is expiring soon:
- Employee Name: [Employee Name]
- Employee ID: [Employee ID]
- Bond Number: [Bond Number]
- Expiry Date: [Expiry Date]

View the document here: https://<replit-app-url>/document/<id>

Please take appropriate action.

Best,
Fidelity Bond Management System
Please implement this web application on Replit using Python Flask, SQLite, and Tailwind CSS. Provide the full source code wrapped in an appropriate artifact tag, including all necessary files (app.py, HTML templates, requirements.txt, etc.). Include a README.md with instructions for setup, running, and deploying on Replit. Ensure the app is fully functional, with authentication, file uploads, search, and email notifications (using a configurable SMTP setup). Test the app to confirm it runs on Replit’s free tier and provide the deployed URL or deployment steps.

Fidelity Bond Management Web App
python
Show inline
requirements.txt
plain
Show inline
templates/index.html
html
Show inline
templates/login.html
html
Show inline
templates/upload.html
html
Show inline
templates/search.html
html
Show inline
templates/document.html
html
Show inline
templates/settings.html
html
Show inline
README.md
markdown
Show inline
Explanation of the Solution
Why Flask on Replit:

Simplicity: Flask is lightweight and well-suited for Replit’s Python environment, making it ideal for a small-scale web app.
SQLite: Built into Python, it simplifies database setup without external dependencies.
Replit’s Environment: Supports file storage, Secrets for configuration, and automatic HTTPS, aligning with your security needs.
Tailwind CSS: Provides a modern, responsive UI via CDN, minimizing setup complexity.
Key Features Implemented:

Authentication: Basic username/password login with bcrypt hashing (default: admin/admin123).
Upload: Stores files in /uploads with unique names (UUID prefix) and metadata in SQLite.
Search: Case-insensitive search across Employee ID, Name, and Bond Number with results in a table.
Email Notifications: Daily checks for expiring bonds using the schedule library, sending emails via smtplib (Gmail SMTP).
Settings: Configurable admin email and notification period, stored in SQLite.
Security: Input validation, secure file handling, and HTTPS via Replit.
Additional Features: Document deletion with confirmation and a document view page.
How to Set Up and Run on Replit:

Create a new Python project on Replit.
Copy the provided files (app.py, templates/, requirements.txt, README.md) into the project.
Create an uploads folder in the file explorer.
In Replit’s Shell, run pip install -r requirements.txt.
Add environment variables in Replit’s Secrets:
SECRET_KEY: A secure key (e.g., your-secret-key).
SMTP_SERVER: smtp.gmail.com.
SMTP_PORT: 587.
SMTP_USER: Your Gmail email.
SMTP_PASSWORD: A Gmail App Password (generate from Google Account settings).
REPLIT_APP_URL: Your Replit app’s URL (e.g., your-app.replit.app).
Click “Run” to start the app. Access it via the Replit-generated URL.
Log in with admin/admin123, then use the upload, search, and settings pages.
Notes for Production:

Email Setup: You must configure Gmail SMTP with an App Password. Without this, emails will fail (logged to console).
Storage: Replit’s free tier has limited disk space; monitor /uploads usage.
Persistence: Replit’s file system is persistent, but backups are recommended for production.
Scaling: For high usage, consider a paid Replit plan to avoid pauses or upgrade to a cloud host (e.g., Heroku).
Sample Email Output:
When a bond is within the notification period, an email like this is sent:

text

Copy
Subject: Fidelity Bond Expiration Notification

Dear Administrator,

The fidelity bond for the following employee is expiring soon:
- Employee Name: John Doe
- Employee ID: EMP123
- Bond Number: BOND456
- Expiry Date: 2025-06-15

View the document here: https://your-app.replit.app/document/1

Best,
Fidelity Bond Management System