import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import PricingTiers from "@/components/pricing-tiers";
import AuthModal from "@/components/auth-modal";
import { ArrowRight, Users, Building } from "lucide-react";

export default function Pricing() {
  const [selectedUserType, setSelectedUserType] = useState<'customer' | 'supplier'>('customer');
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('register');

  const handleSelectPlan = (planId: string) => {
    // If it's a free plan, go straight to registration
    if (planId.includes('free') || planId.includes('basic')) {
      setAuthMode('register');
      setAuthModalOpen(true);
    } else {
      // For paid plans, we'll need payment processing
      setAuthMode('register');
      setAuthModalOpen(true);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-white">
      {/* Hero Section */}
      <div className="relative py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Choose the Perfect Plan for Your Needs
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Whether you're looking for local services or providing them, Hello Luzon! 
            has the right plan to help you succeed in the Philippine market.
          </p>

          {/* User Type Selection */}
          <div className="flex justify-center mb-12">
            <div className="bg-white rounded-lg p-2 shadow-sm border">
              <div className="flex gap-2">
                <Button
                  variant={selectedUserType === 'customer' ? 'default' : 'ghost'}
                  onClick={() => setSelectedUserType('customer')}
                  className={`flex items-center gap-2 px-6 py-3 ${
                    selectedUserType === 'customer' 
                      ? 'bg-filipino-orange hover:bg-orange-600 text-white' 
                      : ''
                  }`}
                >
                  <Users className="w-5 h-5" />
                  I Need Services
                </Button>
                <Button
                  variant={selectedUserType === 'supplier' ? 'default' : 'ghost'}
                  onClick={() => setSelectedUserType('supplier')}
                  className={`flex items-center gap-2 px-6 py-3 ${
                    selectedUserType === 'supplier' 
                      ? 'bg-filipino-orange hover:bg-orange-600 text-white' 
                      : ''
                  }`}
                >
                  <Building className="w-5 h-5" />
                  I Provide Services
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Tiers */}
      <div className="pb-16">
        <PricingTiers 
          userType={selectedUserType} 
          onSelectPlan={handleSelectPlan}
        />
      </div>

      {/* FAQ Section */}
      <div className="bg-white py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Frequently Asked Questions
          </h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold text-lg mb-2">Can I change my plan later?</h3>
              <p className="text-gray-600 mb-6">
                Yes! You can upgrade or downgrade your plan at any time. Changes take effect 
                immediately for upgrades, or at the end of your billing cycle for downgrades.
              </p>

              <h3 className="font-semibold text-lg mb-2">What payment methods do you accept?</h3>
              <p className="text-gray-600 mb-6">
                We accept all major credit cards, PayPal, GCash, Maya, and bank transfers. 
                All payments are processed securely.
              </p>

              <h3 className="font-semibold text-lg mb-2">Is there a setup fee?</h3>
              <p className="text-gray-600">
                No setup fees! You only pay for your chosen plan. We also offer a 7-day 
                free trial for all paid plans.
              </p>
            </div>

            <div>
              <h3 className="font-semibold text-lg mb-2">Can I cancel anytime?</h3>
              <p className="text-gray-600 mb-6">
                Absolutely! There are no long-term contracts. You can cancel your subscription 
                at any time from your account settings.
              </p>

              <h3 className="font-semibold text-lg mb-2">Do you offer refunds?</h3>
              <p className="text-gray-600 mb-6">
                We offer a 30-day money-back guarantee for all paid plans. If you're not 
                satisfied, we'll refund your payment in full.
              </p>

              <h3 className="font-semibold text-lg mb-2">Need help choosing a plan?</h3>
              <p className="text-gray-600">
                Our support team is here to help! Contact <NAME_EMAIL> 
                or use the chat widget on any page.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-filipino-orange py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-orange-100 mb-8">
            Join thousands of satisfied {selectedUserType === 'customer' ? 'customers' : 'service providers'} 
            {" "}across Luzon today!
          </p>
          <Button
            onClick={() => {
              setAuthMode('register');
              setAuthModalOpen(true);
            }}
            className="bg-white text-filipino-orange hover:bg-gray-100 text-lg px-8 py-3"
          >
            Start Your Free Trial
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </div>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        defaultTab={authMode}
        userType={selectedUserType}
      />
    </div>
  );
}