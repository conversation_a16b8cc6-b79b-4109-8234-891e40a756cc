import { useState } from "react";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search } from "lucide-react";
import { PROVINCES } from "@/lib/types";

interface SearchBarProps {
  onSearch?: (query: string, province?: string) => void;
  defaultQuery?: string;
  defaultProvince?: string;
  className?: string;
}

export default function SearchBar({ 
  onSearch, 
  defaultQuery = "", 
  defaultProvince = "",
  className = ""
}: SearchBarProps) {
  const [, setLocation] = useLocation();
  const [query, setQuery] = useState(defaultQuery);
  const [province, setProvince] = useState(defaultProvince);

  const handleSearch = () => {
    if (onSearch) {
      onSearch(query, province === "all" ? "" : province);
    } else {
      // Navigate to suppliers page with search params
      const params = new URLSearchParams();
      if (query) params.set("q", query);
      if (province && province !== "all") params.set("province", province);
      setLocation(`/suppliers?${params.toString()}`);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const popularSearches = [
    "Wedding Caterers",
    "Event Photographers", 
    "Party Decorations"
  ];

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      <div className="bg-white rounded-2xl shadow-2xl p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="text"
              placeholder="What kind of event supplier are you looking for?"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="pl-12 pr-4 py-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-filipino-orange text-gray-900 h-12"
            />
          </div>
          <div className="flex gap-2">
            <Select value={province} onValueChange={setProvince}>
              <SelectTrigger className="px-4 py-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-filipino-orange text-gray-700 h-12 w-48">
                <SelectValue placeholder="All Regions" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Regions</SelectItem>
                {PROVINCES.map((prov) => (
                  <SelectItem key={prov} value={prov}>
                    {prov}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button 
              onClick={handleSearch}
              className="bg-filipino-orange text-white px-8 py-4 rounded-xl hover:bg-orange-600 transition-colors duration-300 font-semibold h-12"
            >
              Search
            </Button>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2 mt-4">
          <span className="text-gray-600 text-sm">Popular searches:</span>
          {popularSearches.map((search) => (
            <button
              key={search}
              onClick={() => {
                setQuery(search);
                if (onSearch) {
                  onSearch(search, province);
                }
              }}
              className="text-filipino-orange text-sm hover:underline"
            >
              {search}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
