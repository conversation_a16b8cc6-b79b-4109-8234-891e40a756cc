import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import AiChat from "@/components/ai-chat";
import NotFound from "@/pages/not-found";
import Home from "@/pages/home";
import Suppliers from "@/pages/suppliers";
import SupplierProfile from "@/pages/supplier-profile";
import Admin from "@/pages/admin";
import Recommendations from "@/pages/recommendations";
import JoinUs from "@/pages/join-us";
import Pricing from "@/pages/pricing";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/suppliers" component={Suppliers} />
      <Route path="/suppliers/:id" component={SupplierProfile} />
      <Route path="/category/:category" component={Suppliers} />
      <Route path="/recommendations" component={Recommendations} />
      <Route path="/join-us" component={JoinUs} />
      <Route path="/pricing" component={Pricing} />
      <Route path="/admin" component={Admin} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Router />
        <AiChat />
        <Toaster />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
