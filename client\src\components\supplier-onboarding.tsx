import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, CheckCircle, ArrowRight, ArrowLeft, Sparkles } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { CATEGORIES, PROVINCES } from "@/lib/types";

interface OnboardingData {
  businessName: string;
  category: string;
  province: string;
  city: string;
  barangay: string;
  yearsExperience: string;
  specialties: string;
  priceRange: string;
  phone: string;
  email: string;
  website: string;
  socialMedia: string;
  portfolioLinks: string;
  awards: string;
  generatedDescription: string;
}

interface ChatMessage {
  role: 'assistant' | 'user';
  content: string;
  suggestions?: string[];
}

export default function SupplierOnboarding() {
  const [step, setStep] = useState(1);
  const [data, setData] = useState<OnboardingData>({
    businessName: '',
    category: '',
    province: '',
    city: '',
    barangay: '',
    yearsExperience: '',
    specialties: '',
    priceRange: '',
    phone: '',
    email: '',
    website: '',
    socialMedia: '',
    portfolioLinks: '',
    awards: '',
    generatedDescription: ''
  });
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([
    {
      role: 'assistant',
      content: 'Welcome to Hello Luzon! 🎉 I\'m here to help you create an amazing profile that will attract customers across Region 2. Let\'s start with the basics - what\'s your business name and what service do you provide?',
      suggestions: ['Wedding Photography', 'Home Electrical Services', 'Event Catering', 'House Cleaning', 'Tutoring Services']
    }
  ]);
  const [currentInput, setCurrentInput] = useState('');
  
  const { toast } = useToast();

  const generateDescriptionMutation = useMutation({
    mutationFn: async (supplierData: Partial<OnboardingData>) => {
      const response = await apiRequest('/api/suppliers/generate-profile', {
        method: 'POST',
        body: JSON.stringify({ supplierInfo: supplierData })
      });
      return response;
    },
    onSuccess: (result) => {
      setData(prev => ({ ...prev, generatedDescription: result.description }));
      setChatMessages(prev => [...prev, {
        role: 'assistant',
        content: `Great! I've created a professional description for your business. Here it is:\n\n"${result.description}"\n\nDoes this capture your business well? Feel free to edit it or ask me to adjust anything!`
      }]);
    },
    onError: () => {
      toast({
        title: "Generation Error",
        description: "Couldn't generate description. Please try again.",
        variant: "destructive"
      });
    }
  });

  const submitProfileMutation = useMutation({
    mutationFn: async (profileData: OnboardingData) => {
      const supplierData = {
        name: profileData.businessName,
        category: profileData.category,
        description: profileData.generatedDescription,
        location: `${profileData.city}, ${profileData.barangay}`,
        province: profileData.province,
        phone: profileData.phone,
        email: profileData.email,
        website: profileData.website || undefined,
        startingPrice: parseInt(profileData.priceRange.replace(/[^\d]/g, '')) || 0,
        verified: false,
        featured: false
      };
      
      const response = await apiRequest('/api/suppliers', {
        method: 'POST',
        body: JSON.stringify(supplierData)
      });
      return response;
    },
    onSuccess: () => {
      toast({
        title: "Profile Created! 🎉",
        description: "Your supplier profile is now live on Hello Luzon!",
      });
      setStep(6); // Success step
    },
    onError: () => {
      toast({
        title: "Submission Error",
        description: "Couldn't create your profile. Please try again.",
        variant: "destructive"
      });
    }
  });

  const handleChatSubmit = () => {
    if (!currentInput.trim()) return;
    
    setChatMessages(prev => [...prev, { role: 'user', content: currentInput }]);
    
    // Simple chat flow logic based on current step
    const input = currentInput.toLowerCase();
    
    if (step === 1) {
      // Extract business name and category
      const foundCategory = CATEGORIES.find(cat => 
        input.includes(cat.toLowerCase()) || 
        currentInput.includes(cat)
      );
      
      if (foundCategory) {
        setData(prev => ({ 
          ...prev, 
          businessName: currentInput.split(' ').slice(0, -2).join(' ') || currentInput,
          category: foundCategory 
        }));
        setChatMessages(prev => [...prev, {
          role: 'assistant',
          content: `Perfect! ${foundCategory} is a great service. Now, where are you located? Which province and city in Region 2?`,
          suggestions: PROVINCES
        }]);
        setStep(2);
      } else {
        setChatMessages(prev => [...prev, {
          role: 'assistant',
          content: 'I see your business name! What type of service do you provide? Here are some popular categories:',
          suggestions: CATEGORIES.slice(0, 8)
        }]);
      }
    } else if (step === 2) {
      // Extract location
      const foundProvince = PROVINCES.find(prov => 
        input.includes(prov.toLowerCase())
      );
      
      if (foundProvince) {
        setData(prev => ({ ...prev, province: foundProvince }));
        setChatMessages(prev => [...prev, {
          role: 'assistant',
          content: `Great! You're in ${foundProvince}. What city and barangay specifically? Also, how many years of experience do you have in ${data.category}?`
        }]);
        setStep(3);
      } else {
        setChatMessages(prev => [...prev, {
          role: 'assistant',
          content: 'Which province in Region 2 are you located in?',
          suggestions: PROVINCES
        }]);
      }
    } else if (step === 3) {
      // Collect experience and location details
      const experienceMatch = currentInput.match(/(\d+)\s*years?/i);
      if (experienceMatch) {
        setData(prev => ({ ...prev, yearsExperience: experienceMatch[1] }));
      }
      
      setChatMessages(prev => [...prev, {
        role: 'assistant',
        content: 'Excellent! Now tell me about your specialties and what makes your service unique. What sets you apart from competitors?'
      }]);
      setStep(4);
    } else if (step === 4) {
      // Collect specialties
      setData(prev => ({ ...prev, specialties: currentInput }));
      setChatMessages(prev => [...prev, {
        role: 'assistant',
        content: 'That sounds amazing! What\'s your typical price range or starting price? And please share your contact details (phone, email, website if you have one).'
      }]);
      setStep(5);
    }
    
    setCurrentInput('');
  };

  const generateDescription = () => {
    if (!data.businessName || !data.category) {
      toast({
        title: "Missing Information",
        description: "Please provide business name and category first.",
        variant: "destructive"
      });
      return;
    }
    
    generateDescriptionMutation.mutate(data);
  };

  if (step === 6) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <CardTitle className="text-2xl text-green-700">Welcome to Hello Luzon! 🎉</CardTitle>
          <CardDescription>
            Your supplier profile is now live and ready to receive inquiries from customers across Region 2!
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-gray-600 mb-6">
            Customers can now find and contact you for {data.category} services in {data.province}. 
            We'll notify you when you receive inquiries.
          </p>
          <Button onClick={() => window.location.href = '/suppliers'} className="bg-filipino-orange hover:bg-orange-600">
            View Your Profile
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-filipino-orange" />
            Supplier Onboarding Assistant
          </CardTitle>
          <CardDescription>
            Let me help you create an amazing profile that attracts customers across Region 2
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Chat Interface */}
          <div className="bg-gray-50 rounded-lg p-4 h-96 overflow-y-auto mb-4">
            {chatMessages.map((message, index) => (
              <div key={index} className={`mb-4 ${message.role === 'user' ? 'text-right' : 'text-left'}`}>
                <div className={`inline-block p-3 rounded-lg max-w-xs ${
                  message.role === 'user' 
                    ? 'bg-filipino-orange text-white' 
                    : 'bg-white border shadow-sm'
                }`}>
                  {message.content}
                </div>
                {message.suggestions && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {message.suggestions.map((suggestion, idx) => (
                      <Button
                        key={idx}
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentInput(suggestion)}
                        className="text-xs"
                      >
                        {suggestion}
                      </Button>
                    ))}
                  </div>
                )}
              </div>
            ))}
            {generateDescriptionMutation.isPending && (
              <div className="text-left mb-4">
                <div className="inline-block p-3 rounded-lg bg-white border shadow-sm">
                  <Loader2 className="w-4 h-4 animate-spin inline mr-2" />
                  Creating your professional description...
                </div>
              </div>
            )}
          </div>

          {/* Input Area */}
          <div className="flex gap-2">
            <Input
              placeholder="Type your response..."
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleChatSubmit()}
              className="flex-1"
            />
            <Button onClick={handleChatSubmit} disabled={!currentInput.trim()}>
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Form Preview/Edit */}
      {step >= 4 && (
        <Card>
          <CardHeader>
            <CardTitle>Profile Preview & Contact Details</CardTitle>
            <CardDescription>
              Review and complete your information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  value={data.phone}
                  onChange={(e) => setData(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="09XX XXX XXXX"
                />
              </div>
              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={data.email}
                  onChange={(e) => setData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="website">Website (Optional)</Label>
                <Input
                  id="website"
                  value={data.website}
                  onChange={(e) => setData(prev => ({ ...prev, website: e.target.value }))}
                  placeholder="https://your-website.com"
                />
              </div>
              <div>
                <Label htmlFor="priceRange">Starting Price</Label>
                <Input
                  id="priceRange"
                  value={data.priceRange}
                  onChange={(e) => setData(prev => ({ ...prev, priceRange: e.target.value }))}
                  placeholder="₱5,000"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="city">City & Barangay</Label>
              <Input
                id="city"
                value={data.city}
                onChange={(e) => setData(prev => ({ ...prev, city: e.target.value }))}
                placeholder="City, Barangay"
              />
            </div>

            {data.generatedDescription && (
              <div>
                <Label htmlFor="description">Generated Business Description</Label>
                <Textarea
                  id="description"
                  value={data.generatedDescription}
                  onChange={(e) => setData(prev => ({ ...prev, generatedDescription: e.target.value }))}
                  rows={4}
                  className="mt-1"
                />
              </div>
            )}

            <div className="flex gap-2">
              <Button 
                onClick={generateDescription}
                disabled={generateDescriptionMutation.isPending}
                variant="outline"
                className="flex items-center gap-2"
              >
                {generateDescriptionMutation.isPending ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Sparkles className="w-4 h-4" />
                )}
                Generate AI Description
              </Button>
              
              {data.generatedDescription && (
                <Button 
                  onClick={() => submitProfileMutation.mutate(data)}
                  disabled={submitProfileMutation.isPending || !data.phone || !data.email}
                  className="bg-filipino-orange hover:bg-orange-600"
                >
                  {submitProfileMutation.isPending ? (
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  ) : null}
                  Create Profile
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}