import OpenAI from "openai";
import type { Review } from "@shared/schema";

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

export class FeedbackAnalyzer {
  async generateSupplierSummary(reviews: Review[], supplierCategory: string): Promise<{
    summary: string;
    strengths: string[];
    improvements: string[];
    averageRating: number;
    totalReviews: number;
  }> {
    try {
      if (reviews.length === 0) {
        return {
          summary: "No reviews yet. Be the first to share your experience!",
          strengths: [],
          improvements: [],
          averageRating: 0,
          totalReviews: 0
        };
      }

      const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;
      const reviewTexts = reviews.map(review => review.comment).join("\n");

      const categoryContext = this.getCategoryContext(supplierCategory);

      const prompt = `Analyze customer reviews for a ${supplierCategory} service provider and create a helpful summary for potential customers.

Reviews to analyze:
${reviewTexts}

Category-specific focus: ${categoryContext}

Create a concise summary that:
1. Highlights the main positive themes customers mention
2. Notes any common areas for improvement (if any)
3. Uses authentic language that feels genuine, not overly promotional
4. Focuses on specific aspects relevant to ${supplierCategory} services

Return JSON format:
{
  "summary": "Brief 1-2 sentence overview of what customers are saying",
  "strengths": ["Top 3-4 positive themes customers mention"],
  "improvements": ["Areas customers suggest for improvement (if any)"]
}`;

      const completion = await openai.chat.completions.create({
        model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages: [{ role: "user", content: prompt }],
        response_format: { type: "json_object" },
        temperature: 0.3,
        max_tokens: 400
      });

      const analysis = JSON.parse(completion.choices[0].message.content || '{}');

      return {
        summary: analysis.summary || "Customers have shared positive feedback about this service provider.",
        strengths: analysis.strengths || [],
        improvements: analysis.improvements || [],
        averageRating: Math.round(averageRating * 10) / 10,
        totalReviews: reviews.length
      };

    } catch (error) {
      console.error("Feedback analysis error:", error);
      return {
        summary: "Feedback analysis unavailable at the moment.",
        strengths: [],
        improvements: [],
        averageRating: reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length || 0,
        totalReviews: reviews.length
      };
    }
  }

  private getCategoryContext(category: string): string {
    const contexts = {
      "Catering": "Focus on food quality, taste, presentation, service timing, and value for money",
      "Photography": "Focus on creativity, photo quality, professionalism, communication, and delivery time",
      "Videography": "Focus on video quality, creativity, storytelling, technical skills, and delivery",
      "Sound & Lighting": "Focus on equipment quality, setup efficiency, technical support, and reliability",
      "Decorations": "Focus on design creativity, setup quality, theme execution, and timeline adherence",
      "Beauty & Wellness": "Focus on skill level, comfort during service, professionalism, and results",
      "Electrician": "Focus on work quality, safety, timeliness, problem-solving, and fair pricing",
      "Plumber": "Focus on work quality, response time, problem resolution, cleanliness, and reliability",
      "Carpenter": "Focus on craftsmanship, attention to detail, project timeline, and communication",
      "Housekeeping & Cleaning": "Focus on thoroughness, reliability, trustworthiness, and efficiency",
      "Tutoring & Education": "Focus on teaching effectiveness, patience, communication, and student progress",
      "Web Design & Development": "Focus on technical skills, creativity, communication, timeline, and ongoing support"
    };

    return contexts[category as keyof typeof contexts] || "Focus on service quality, professionalism, communication, and value";
  }

  async detectTrends(reviews: Review[]): Promise<{
    positiveKeywords: string[];
    concernKeywords: string[];
    seasonalPatterns: string;
  }> {
    try {
      if (reviews.length < 3) {
        return {
          positiveKeywords: [],
          concernKeywords: [],
          seasonalPatterns: "Not enough data for trend analysis"
        };
      }

      const recentReviews = reviews.slice(-10); // Last 10 reviews
      const reviewTexts = recentReviews.map(review => review.comment).join(" ");

      const prompt = `Analyze these recent customer reviews to identify trends and patterns:

${reviewTexts}

Extract:
1. Most frequently mentioned positive keywords/themes
2. Any recurring concerns or issues
3. Any seasonal or timing patterns mentioned

Return JSON format:
{
  "positiveKeywords": ["top positive themes"],
  "concernKeywords": ["recurring concerns if any"],
  "seasonalPatterns": "brief note about timing patterns or 'No clear patterns detected'"
}`;

      const completion = await openai.chat.completions.create({
        model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages: [{ role: "user", content: prompt }],
        response_format: { type: "json_object" },
        temperature: 0.3,
        max_tokens: 300
      });

      const trends = JSON.parse(completion.choices[0].message.content || '{}');

      return {
        positiveKeywords: trends.positiveKeywords || [],
        concernKeywords: trends.concernKeywords || [],
        seasonalPatterns: trends.seasonalPatterns || "No clear patterns detected"
      };

    } catch (error) {
      console.error("Trend analysis error:", error);
      return {
        positiveKeywords: [],
        concernKeywords: [],
        seasonalPatterns: "Trend analysis unavailable"
      };
    }
  }
}

export const feedbackAnalyzer = new FeedbackAnalyzer();