import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import Navbar from "@/components/navbar";
import SearchBar from "@/components/search-bar";
import SupplierCard from "@/components/supplier-card";
import CategoryCard from "@/components/category-card";
import ContactForm from "@/components/contact-form";
import { Button } from "@/components/ui/button";
import { ArrowRight, Search, MessageCircle, Heart } from "lucide-react";
import type { Supplier, Category } from "@shared/schema";

export default function Home() {
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);

  const { data: featuredSuppliers = [], isLoading: suppliersLoading } = useQuery<Supplier[]>({
    queryKey: ["/api/suppliers/featured"],
  });

  const { data: categories = [], isLoading: categoriesLoading } = useQuery<Category[]>({
    queryKey: ["/api/categories"],
  });

  const handleContact = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
  };

  const testimonials = [
    {
      name: "<PERSON> & Robert <PERSON>",
      location: "Tuguegarao, Cagayan",
      rating: 5,
      comment: "Hello Luzon made planning our wedding so much easier! We found amazing suppliers in Cagayan and our special day was perfect.",
      initials: "MR"
    },
    {
      name: "Jose Santos",
      location: "Event Photographer, Isabela",
      rating: 5,
      comment: "As a supplier, Hello Luzon has helped me connect with so many new clients. The platform is easy to use and brings real results.",
      initials: "JS"
    },
    {
      name: "Ana <PERSON>",
      location: "Event Coordinator, Nueva Vizcaya",
      rating: 5,
      comment: "Amazing platform! Found the perfect caterer for our company's anniversary celebration. Everything was seamless and delicious.",
      initials: "AL"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-filipino-orange to-orange-600 text-white py-20 min-h-[500px] flex items-center">
        <div className="absolute inset-0 opacity-10 bg-repeat" 
             style={{backgroundImage: "url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"60\" height=\"60\" viewBox=\"0 0 60 60\"><circle cx=\"30\" cy=\"30\" r=\"2\" fill=\"%23ffffff\"/></svg>')"}}
        ></div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="font-poppins font-bold text-4xl md:text-6xl mb-6 text-filipino-orange drop-shadow-lg">
            Find Perfect Event Suppliers in Luzon
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-orange-700 drop-shadow-md">
            Connect with trusted local suppliers for weddings, birthdays, corporate events, and more across Region 2
          </p>
          
          <SearchBar />
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="font-poppins font-bold text-3xl text-center mb-12 text-gray-900">
            Browse by Category
          </h2>
          
          {categoriesLoading ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-300 rounded-2xl h-48 mb-2"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {categories.map((category, index) => (
                <CategoryCard
                  key={category.id}
                  category={category}
                  isPopular={index === 0} // Make first category popular
                />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Featured Suppliers Section */}
      <section className="py-16 bg-warm-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-12">
            <h2 className="font-poppins font-bold text-3xl text-gray-900">
              Featured Suppliers
            </h2>
            <Button variant="ghost" className="text-filipino-orange hover:text-orange-600 font-semibold">
              View All <ArrowRight className="ml-1 h-4 w-4" />
            </Button>
          </div>

          {suppliersLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-300 rounded-2xl h-64 mb-4"></div>
                  <div className="bg-gray-300 h-4 rounded mb-2"></div>
                  <div className="bg-gray-300 h-3 rounded mb-4"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredSuppliers.map((supplier) => (
                <SupplierCard
                  key={supplier.id}
                  supplier={supplier}
                  onContact={handleContact}
                />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="font-poppins font-bold text-3xl text-center mb-12 text-gray-900">
            How Hello Luzon! Works
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-filipino-orange/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <Search className="text-filipino-orange h-8 w-8" />
              </div>
              <h3 className="font-poppins font-semibold text-xl mb-4 text-gray-900">Search & Discover</h3>
              <p className="text-gray-600 leading-relaxed">
                Use our smart search to find the perfect event suppliers in your area. Filter by budget, location, and service type.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-filipino-orange/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <MessageCircle className="text-filipino-orange h-8 w-8" />
              </div>
              <h3 className="font-poppins font-semibold text-xl mb-4 text-gray-900">Connect & Compare</h3>
              <p className="text-gray-600 leading-relaxed">
                Read reviews, compare prices, and directly contact suppliers that match your needs and budget.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-filipino-orange/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <Heart className="text-filipino-orange h-8 w-8" />
              </div>
              <h3 className="font-poppins font-semibold text-xl mb-4 text-gray-900">Book & Celebrate</h3>
              <p className="text-gray-600 leading-relaxed">
                Book your chosen suppliers and create unforgettable events. Leave reviews to help other customers.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-warm-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="font-poppins font-bold text-3xl text-center mb-12 text-gray-900">
            What Our Customers Say
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex text-filipino-gold mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <span key={i}>★</span>
                  ))}
                </div>
                <p className="text-gray-600 mb-4 italic">
                  "{testimonial.comment}"
                </p>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-filipino-orange/20 rounded-full flex items-center justify-center mr-3">
                    <span className="font-semibold text-filipino-orange">{testimonial.initials}</span>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">{testimonial.name}</p>
                    <p className="text-gray-500 text-sm">{testimonial.location}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-br from-filipino-green to-green-700 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="font-poppins font-bold text-3xl md:text-4xl mb-6">
            Ready to Plan Your Perfect Event?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of satisfied customers who found their ideal event suppliers on Hello Luzon!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-filipino-gold text-gray-900 hover:bg-yellow-400 px-8 py-4 text-lg">
              <Search className="mr-2 h-5 w-5" />
              Find Suppliers Now
            </Button>
            <Button variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-filipino-green px-8 py-4 text-lg">
              Become a Supplier
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-filipino-orange rounded flex items-center justify-center">
                  <span className="text-white font-bold">HL</span>
                </div>
                <span className="font-poppins font-bold text-2xl">Hello Luzon!</span>
              </div>
              <p className="text-gray-300 mb-6 max-w-md">
                Connecting you with the best local event suppliers across Luzon. Making your special occasions memorable and stress-free.
              </p>
            </div>

            <div>
              <h3 className="font-poppins font-semibold text-lg mb-4">Quick Links</h3>
              <ul className="space-y-2 text-gray-300">
                <li><a href="/suppliers" className="hover:text-filipino-orange transition-colors">Browse Suppliers</a></li>
                <li><a href="#" className="hover:text-filipino-orange transition-colors">Event Planning Guide</a></li>
                <li><a href="#" className="hover:text-filipino-orange transition-colors">How It Works</a></li>
                <li><a href="#" className="hover:text-filipino-orange transition-colors">Pricing</a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-poppins font-semibold text-lg mb-4">Support</h3>
              <ul className="space-y-2 text-gray-300">
                <li><a href="#" className="hover:text-filipino-orange transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-filipino-orange transition-colors">Contact Us</a></li>
                <li><a href="#" className="hover:text-filipino-orange transition-colors">Terms of Service</a></li>
                <li><a href="#" className="hover:text-filipino-orange transition-colors">Privacy Policy</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-8 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm">
                © 2024 Hello Luzon! All rights reserved.
              </p>
              <div className="flex items-center space-x-4 mt-4 md:mt-0">
                <span className="text-gray-400 text-sm">Serving:</span>
                <div className="flex space-x-2 text-sm">
                  {["Cagayan", "Isabela", "Nueva Vizcaya"].map((province) => (
                    <span key={province} className="bg-filipino-orange/20 text-filipino-orange px-2 py-1 rounded">
                      {province}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Contact Form Modal */}
      {selectedSupplier && (
        <ContactForm
          supplier={selectedSupplier}
          isOpen={!!selectedSupplier}
          onClose={() => setSelectedSupplier(null)}
        />
      )}
    </div>
  );
}
