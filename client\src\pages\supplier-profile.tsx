import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { useP<PERSON><PERSON>, Link } from "wouter";
import Navbar from "@/components/navbar";
import ContactForm from "@/components/contact-form";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  Star, 
  Heart,
  ArrowLeft,
  MessageCircle,
  Calendar,
  Users
} from "lucide-react";
import type { Supplier, Review } from "@shared/schema";

export default function SupplierProfile() {
  const { id } = useParams();
  const [showContactForm, setShowContactForm] = useState(false);

  const { data: supplier, isLoading: supplierLoading, error } = useQuery<Supplier>({
    queryKey: [`/api/suppliers/${id}`],
    enabled: !!id,
  });

  const { data: reviews = [], isLoading: reviewsLoading } = useQuery<Review[]>({
    queryKey: [`/api/suppliers/${id}/reviews`],
    enabled: !!id,
  });

  if (supplierLoading) {
    return (
      <div className="min-h-screen bg-warm-gray">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Skeleton className="h-8 w-32 mb-6" />
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <Skeleton className="h-64 w-full rounded-2xl mb-6" />
              <Skeleton className="h-8 w-64 mb-4" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4 mb-6" />
            </div>
            <div>
              <Skeleton className="h-40 w-full rounded-lg" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !supplier) {
    return (
      <div className="min-h-screen bg-warm-gray">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Supplier Not Found</h1>
            <p className="text-gray-600 mb-6">The supplier you're looking for doesn't exist.</p>
            <Link href="/suppliers">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Suppliers
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-warm-gray">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <Link href="/suppliers">
          <Button variant="ghost" className="mb-6">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Suppliers
          </Button>
        </Link>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Hero Image */}
            <div className="relative rounded-2xl overflow-hidden mb-6">
              <img 
                src={supplier.imageUrl} 
                alt={supplier.name}
                className="w-full h-64 md:h-80 object-cover"
              />
              <div className="absolute top-4 left-4">
                {supplier.verified && (
                  <Badge className="bg-filipino-gold text-gray-900 hover:bg-filipino-gold">
                    <Star className="h-3 w-3 mr-1 fill-current" />
                    Verified
                  </Badge>
                )}
              </div>
              <div className="absolute top-4 right-4">
                <Button
                  variant="ghost"
                  size="icon"
                  className="bg-white/80 hover:bg-white text-gray-700 rounded-full"
                >
                  <Heart className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Supplier Info */}
            <Card className="mb-6">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h1 className="font-poppins font-bold text-3xl text-gray-900 mb-2">
                      {supplier.name}
                    </h1>
                    <div className="flex items-center gap-4 text-gray-600">
                      <Badge variant="outline">{supplier.category}</Badge>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {supplier.location}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center mb-2">
                      <Star className="h-5 w-5 text-filipino-gold fill-current mr-1" />
                      <span className="font-bold text-lg">{supplier.rating}</span>
                      <span className="text-gray-500 ml-1">({supplier.reviewCount} reviews)</span>
                    </div>
                    <div className="text-filipino-orange font-bold text-xl">
                      Starting at ₱{supplier.startingPrice.toLocaleString()}
                    </div>
                  </div>
                </div>

                <p className="text-gray-600 leading-relaxed">
                  {supplier.description}
                </p>
              </CardContent>
            </Card>

            {/* Reviews */}
            <Card>
              <CardHeader>
                <CardTitle>Customer Reviews</CardTitle>
              </CardHeader>
              <CardContent>
                {reviewsLoading ? (
                  <div className="space-y-4">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="animate-pulse">
                        <Skeleton className="h-4 w-32 mb-2" />
                        <Skeleton className="h-3 w-full mb-1" />
                        <Skeleton className="h-3 w-3/4" />
                      </div>
                    ))}
                  </div>
                ) : reviews.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">
                    No reviews yet. Be the first to leave a review!
                  </p>
                ) : (
                  <div className="space-y-6">
                    {reviews.map((review) => (
                      <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h4 className="font-semibold text-gray-900">{review.customerName}</h4>
                            <p className="text-sm text-gray-500">{review.customerLocation}</p>
                          </div>
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < review.rating
                                    ? "text-filipino-gold fill-current"
                                    : "text-gray-300"
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                        <p className="text-gray-600">{review.comment}</p>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Card */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center">
                  <Phone className="h-4 w-4 text-gray-400 mr-3" />
                  <a href={`tel:${supplier.phone}`} className="text-filipino-orange hover:underline">
                    {supplier.phone}
                  </a>
                </div>
                <div className="flex items-center">
                  <Mail className="h-4 w-4 text-gray-400 mr-3" />
                  <a href={`mailto:${supplier.email}`} className="text-filipino-orange hover:underline">
                    {supplier.email}
                  </a>
                </div>
                {supplier.website && (
                  <div className="flex items-center">
                    <Globe className="h-4 w-4 text-gray-400 mr-3" />
                    <a 
                      href={supplier.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-filipino-orange hover:underline"
                    >
                      Visit Website
                    </a>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardContent className="p-6">
                <Button
                  onClick={() => setShowContactForm(true)}
                  className="w-full bg-filipino-orange hover:bg-orange-600 mb-3"
                  size="lg"
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Send Inquiry
                </Button>
                <Button
                  variant="outline"
                  className="w-full"
                  size="lg"
                >
                  <Phone className="h-4 w-4 mr-2" />
                  Call Now
                </Button>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle>At a Glance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm">Rating</span>
                  </div>
                  <span className="font-semibold">{supplier.rating}/5.0</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <MessageCircle className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm">Reviews</span>
                  </div>
                  <span className="font-semibold">{supplier.reviewCount}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm">Location</span>
                  </div>
                  <span className="font-semibold text-sm">{supplier.province}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Contact Form Modal */}
      <ContactForm
        supplier={supplier}
        isOpen={showContactForm}
        onClose={() => setShowContactForm(false)}
      />
    </div>
  );
}
