import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Check, Star, Crown, Zap, Users, Shield, Phone, MessageCircle } from "lucide-react";

interface PricingTier {
  id: string;
  name: string;
  price: number;
  period: string;
  description: string;
  features: string[];
  highlighted?: boolean;
  badge?: string;
  icon: React.ReactNode;
  limitations?: string[];
}

interface PricingTiersProps {
  userType: 'customer' | 'supplier';
  onSelectPlan?: (planId: string) => void;
}

export default function PricingTiers({ userType, onSelectPlan }: PricingTiersProps) {
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');

  const customerTiers: PricingTier[] = [
    {
      id: 'customer-free',
      name: 'Basic Explorer',
      price: 0,
      period: 'Forever Free',
      description: 'Perfect for occasional service needs',
      icon: <Users className="w-6 h-6 text-blue-500" />,
      features: [
        'Browse all service providers',
        'Basic search and filters',
        'Contact up to 3 suppliers per month',
        'Read reviews and ratings',
        'Basic customer support'
      ],
      limitations: [
        'Limited to 3 supplier contacts monthly',
        'No priority support',
        'Basic search only'
      ]
    },
    {
      id: 'customer-premium',
      name: 'Smart Shopper',
      price: billingPeriod === 'monthly' ? 299 : 2990,
      period: billingPeriod === 'monthly' ? '/month' : '/year',
      description: 'Enhanced features for regular service seekers',
      icon: <Zap className="w-6 h-6 text-orange-500" />,
      highlighted: true,
      badge: 'Most Popular',
      features: [
        'Unlimited supplier contacts',
        'AI-powered smart recommendations',
        'Advanced search filters',
        'Priority customer support',
        'Exclusive deals and discounts',
        'Event planning assistance',
        'Booking history and favorites',
        'Direct messaging with suppliers'
      ]
    },
    {
      id: 'customer-business',
      name: 'Business Elite',
      price: billingPeriod === 'monthly' ? 899 : 8990,
      period: billingPeriod === 'monthly' ? '/month' : '/year',
      description: 'For businesses and frequent event organizers',
      icon: <Crown className="w-6 h-6 text-purple-500" />,
      features: [
        'Everything in Smart Shopper',
        'Dedicated account manager',
        'Bulk booking discounts',
        'Corporate invoicing',
        'Team collaboration tools',
        'Custom service packages',
        '24/7 priority support',
        'Advanced analytics dashboard',
        'Supplier performance insights'
      ]
    }
  ];

  const supplierTiers: PricingTier[] = [
    {
      id: 'supplier-basic',
      name: 'Starter Provider',
      price: 0,
      period: 'Forever Free',
      description: 'Get started with basic listing features',
      icon: <Users className="w-6 h-6 text-blue-500" />,
      features: [
        'Basic business profile',
        'Up to 5 service photos',
        'Customer reviews and ratings',
        'Basic contact inquiries',
        'Monthly performance report'
      ],
      limitations: [
        'Limited to 10 inquiries per month',
        'Basic profile visibility',
        'No featured listings'
      ]
    },
    {
      id: 'supplier-professional',
      name: 'Professional Plus',
      price: billingPeriod === 'monthly' ? 799 : 7990,
      period: billingPeriod === 'monthly' ? '/month' : '/year',
      description: 'Enhanced visibility and customer management',
      icon: <Star className="w-6 h-6 text-orange-500" />,
      highlighted: true,
      badge: 'Recommended',
      features: [
        'Enhanced business profile',
        'Unlimited service photos',
        'Featured in search results',
        'Unlimited customer inquiries',
        'Advanced booking calendar',
        'Customer relationship tools',
        'Weekly performance analytics',
        'Social media integration',
        'Professional badge verification'
      ]
    },
    {
      id: 'supplier-enterprise',
      name: 'Enterprise Leader',
      price: billingPeriod === 'monthly' ? 1999 : 19990,
      period: billingPeriod === 'monthly' ? '/month' : '/year',
      description: 'Complete business management solution',
      icon: <Crown className="w-6 h-6 text-purple-500" />,
      features: [
        'Everything in Professional Plus',
        'Priority placement in searches',
        'Advanced analytics dashboard',
        'Multi-location management',
        'Team member accounts',
        'Custom branding options',
        'API access for integrations',
        'Dedicated success manager',
        '24/7 priority support',
        'White-label booking system'
      ]
    }
  ];

  const currentTiers = userType === 'customer' ? customerTiers : supplierTiers;
  const yearlyDiscount = billingPeriod === 'yearly' ? '2 months free!' : '';

  return (
    <div className="w-full max-w-7xl mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Choose Your {userType === 'customer' ? 'Service Plan' : 'Business Plan'}
        </h2>
        <p className="text-lg text-gray-600 mb-6">
          {userType === 'customer' 
            ? 'Find the perfect plan to discover and book amazing local services'
            : 'Grow your business with the right tools and features'
          }
        </p>

        {/* Billing Period Toggle */}
        <div className="flex items-center justify-center gap-4 mb-8">
          <span className={`text-sm ${billingPeriod === 'monthly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
            Monthly
          </span>
          <button
            onClick={() => setBillingPeriod(billingPeriod === 'monthly' ? 'yearly' : 'monthly')}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              billingPeriod === 'yearly' ? 'bg-filipino-orange' : 'bg-gray-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                billingPeriod === 'yearly' ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
          <span className={`text-sm ${billingPeriod === 'yearly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
            Yearly
          </span>
          {billingPeriod === 'yearly' && (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              Save 17%
            </Badge>
          )}
        </div>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {currentTiers.map((tier) => (
          <Card 
            key={tier.id} 
            className={`relative ${tier.highlighted ? 'border-filipino-orange shadow-lg scale-105' : 'border-gray-200'}`}
          >
            {tier.badge && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-filipino-orange text-white px-3 py-1">
                  {tier.badge}
                </Badge>
              </div>
            )}
            
            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-3">
                {tier.icon}
              </div>
              <CardTitle className="text-xl font-semibold">{tier.name}</CardTitle>
              <div className="mt-2">
                <span className="text-3xl font-bold text-gray-900">
                  ₱{tier.price.toLocaleString()}
                </span>
                <span className="text-gray-500 ml-1">{tier.period}</span>
              </div>
              <CardDescription className="mt-2">{tier.description}</CardDescription>
            </CardHeader>

            <CardContent>
              <ul className="space-y-3 mb-6">
                {tier.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              {tier.limitations && (
                <div className="mb-6">
                  <p className="text-xs text-gray-500 mb-2">Limitations:</p>
                  <ul className="space-y-1">
                    {tier.limitations.map((limitation, index) => (
                      <li key={index} className="text-xs text-gray-500 flex items-start gap-2">
                        <span className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                        {limitation}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <Button
                onClick={() => onSelectPlan?.(tier.id)}
                className={`w-full ${
                  tier.highlighted 
                    ? 'bg-filipino-orange hover:bg-orange-600 text-white' 
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-900'
                }`}
              >
                {tier.price === 0 ? 'Get Started Free' : 'Choose This Plan'}
              </Button>

              {tier.price > 0 && (
                <p className="text-xs text-gray-500 text-center mt-3">
                  7-day free trial • Cancel anytime
                </p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Additional Benefits Section */}
      <div className="mt-12 grid md:grid-cols-3 gap-6">
        <div className="text-center p-6">
          <Shield className="w-8 h-8 text-filipino-orange mx-auto mb-3" />
          <h3 className="font-semibold mb-2">Secure & Trusted</h3>
          <p className="text-sm text-gray-600">All transactions are encrypted and your data is protected</p>
        </div>
        <div className="text-center p-6">
          <Phone className="w-8 h-8 text-filipino-orange mx-auto mb-3" />
          <h3 className="font-semibold mb-2">24/7 Support</h3>
          <p className="text-sm text-gray-600">Get help whenever you need it with our dedicated support team</p>
        </div>
        <div className="text-center p-6">
          <MessageCircle className="w-8 h-8 text-filipino-orange mx-auto mb-3" />
          <h3 className="font-semibold mb-2">Community Driven</h3>
          <p className="text-sm text-gray-600">Join thousands of satisfied customers and providers in Luzon</p>
        </div>
      </div>

      <div className="mt-8 text-center">
        <p className="text-sm text-gray-600">
          All prices are in Philippine Peso (₱). Need a custom plan?{" "}
          <a href="#" className="text-filipino-orange hover:underline">Contact our sales team</a>
        </p>
      </div>
    </div>
  );
}