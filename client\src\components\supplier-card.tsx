import { Heart, Star, MapPin, Phone } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import type { Supplier } from "@shared/schema";
import { Link } from "wouter";

interface SupplierCardProps {
  supplier: Supplier;
  onContact?: (supplier: Supplier) => void;
}

export default function SupplierCard({ supplier, onContact }: SupplierCardProps) {
  const handleContactClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (onContact) {
      onContact(supplier);
    }
  };

  return (
    <Card className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
      <div className="relative">
        <img 
          src={supplier.imageUrl} 
          alt={supplier.name}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute top-4 left-4">
          {supplier.verified && (
            <Badge className="bg-filipino-gold text-gray-900 hover:bg-filipino-gold">
              <Star className="h-3 w-3 mr-1 fill-current" />
              Verified
            </Badge>
          )}
        </div>
        <div className="absolute top-4 right-4">
          <Button
            variant="ghost"
            size="icon"
            className="bg-white/80 hover:bg-white text-gray-700 rounded-full"
          >
            <Heart className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-3">
          <Link href={`/suppliers/${supplier.id}`}>
            <h3 className="font-poppins font-semibold text-lg text-gray-900 hover:text-filipino-orange cursor-pointer transition-colors">
              {supplier.name}
            </h3>
          </Link>
          <div className="flex items-center space-x-1">
            <Star className="h-4 w-4 text-filipino-gold fill-current" />
            <span className="text-gray-700 font-semibold">{supplier.rating}</span>
            <span className="text-gray-500 text-sm">({supplier.reviewCount})</span>
          </div>
        </div>
        
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {supplier.description}
        </p>
        
        <div className="flex items-center text-sm text-gray-500 mb-3">
          <MapPin className="h-4 w-4 mr-1" />
          <span>{supplier.location}</span>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="text-filipino-orange font-semibold">
            Starting at ₱{supplier.startingPrice.toLocaleString()}
          </div>
          <Button 
            onClick={handleContactClick}
            className="bg-filipino-orange text-white hover:bg-orange-600 transition-colors duration-300 text-sm"
          >
            <Phone className="h-3 w-3 mr-1" />
            Contact Now
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
