#!/usr/bin/env node

/**
 * Simple test server to verify basic functionality
 */

import express from 'express';

const app = express();
const port = 5000;

app.use(express.json());

// Basic health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: 'Hello Luzon test server is running!',
    timestamp: new Date().toISOString()
  });
});

// Basic API endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'Hello Luzon API is working!',
    environment: process.env.NODE_ENV || 'development',
    hasDatabase: !!process.env.DATABASE_URL,
    hasOpenAI: !!process.env.OPENAI_API_KEY,
    hasGemini: !!process.env.GEMINI_API_KEY
  });
});

// Serve a simple HTML page
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Hello Luzon - Test Server</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn:hover { background: #0056b3; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>🌟 Hello Luzon - Test Server</h1>
        <div class="status success">
          ✅ Server is running successfully on port ${port}
        </div>
        
        <h2>Environment Status</h2>
        <div class="status ${process.env.DATABASE_URL ? 'success' : 'warning'}">
          ${process.env.DATABASE_URL ? '✅' : '⚠️'} Database URL: ${process.env.DATABASE_URL ? 'Configured' : 'Not configured'}
        </div>
        <div class="status ${process.env.OPENAI_API_KEY ? 'success' : 'warning'}">
          ${process.env.OPENAI_API_KEY ? '✅' : '⚠️'} OpenAI API Key: ${process.env.OPENAI_API_KEY ? 'Configured' : 'Not configured'}
        </div>
        <div class="status ${process.env.GEMINI_API_KEY ? 'success' : 'warning'}">
          ${process.env.GEMINI_API_KEY ? '✅' : '⚠️'} Gemini API Key: ${process.env.GEMINI_API_KEY ? 'Configured' : 'Not configured'}
        </div>
        
        <h2>Test Endpoints</h2>
        <a href="/health" class="btn">Health Check</a>
        <a href="/api/test" class="btn">API Test</a>
        
        <h2>Next Steps</h2>
        <p>To run the full Hello Luzon system:</p>
        <ol>
          <li>Set up a PostgreSQL database</li>
          <li>Get OpenAI and Gemini API keys</li>
          <li>Configure environment variables</li>
          <li>Run: <code>npm run dev</code></li>
        </ol>
        
        <p>See <strong>RUN_SYSTEM.md</strong> for detailed instructions.</p>
      </div>
    </body>
    </html>
  `);
});

app.listen(port, '0.0.0.0', () => {
  console.log(`🌟 Hello Luzon test server running at:`);
  console.log(`   • Local: http://localhost:${port}`);
  console.log(`   • Network: http://0.0.0.0:${port}`);
  console.log(`\n📋 Environment Status:`);
  console.log(`   • NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
  console.log(`   • Database: ${process.env.DATABASE_URL ? 'configured' : 'not configured'}`);
  console.log(`   • OpenAI: ${process.env.OPENAI_API_KEY ? 'configured' : 'not configured'}`);
  console.log(`   • Gemini: ${process.env.GEMINI_API_KEY ? 'configured' : 'not configured'}`);
  console.log(`\n🚀 Ready to serve requests!`);
});
