import { Link } from "wouter";
import { Badge } from "@/components/ui/badge";
import type { Category } from "@shared/schema";

interface CategoryCardProps {
  category: Category;
  isPopular?: boolean;
}

export default function CategoryCard({ category, isPopular = false }: CategoryCardProps) {
  return (
    <Link href={`/category/${category.slug}`}>
      <div className="group cursor-pointer">
        <div className="relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
          <img 
            src={category.imageUrl} 
            alt={category.name}
            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
          <div className="absolute bottom-4 left-4 text-white">
            <h3 className="font-poppins font-semibold text-lg">{category.name}</h3>
            <p className="text-sm opacity-90">{category.supplierCount}+ suppliers</p>
          </div>
          {isPopular && (
            <div className="absolute top-4 right-4">
              <Badge className="bg-filipino-gold text-gray-900 hover:bg-filipino-gold">
                <span className="text-xs font-semibold">Popular</span>
              </Badge>
            </div>
          )}
        </div>
      </div>
    </Link>
  );
}
