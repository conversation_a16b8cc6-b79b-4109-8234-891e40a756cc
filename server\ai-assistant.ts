import OpenAI from "openai";
import { GoogleGenerativeAI } from "@google/generative-ai";

// the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export interface SupplierData {
  name: string;
  description: string;
  category?: string;
  location: string;
  province: string;
  phone: string;
  email: string;
  website?: string;
  imageUrl?: string;
  startingPrice?: number;
  verified?: boolean;
  featured?: boolean;
}

export interface AIEnhancedSupplier extends SupplierData {
  aiSuggestions?: {
    enhancedDescription?: string;
    suggestedCategory?: string;
    qualityScore?: number;
    improvements?: string[];
  };
}

// Available categories for supplier classification
const CATEGORIES = [
  "Catering", "Photography", "Videography", "Event Planning", "Venue Rental",
  "DJ & Music", "Flowers & Decoration", "Transportation", "Wedding Services",
  "Sound & Lighting", "Entertainment", "Security Services"
];

const PROVINCES = [
  "Cagayan", "Isabela", "Nueva Vizcaya", "Quirino", "Batanes"
];

export class AIAssistant {
  // Smart CSV processing with AI enhancement
  async processCSVData(csvText: string): Promise<AIEnhancedSupplier[]> {
    try {
      const prompt = `
You are an AI assistant for Hello Luzon!, a marketplace connecting people with local event suppliers in Region 2 of the Philippines (Cagayan, Isabela, Nueva Vizcaya, Quirino, Batanes).

Process this CSV data and enhance each supplier entry:

CSV Data:
${csvText}

For each supplier, provide:
1. Clean and validate all data
2. Enhance the business description to be more appealing and professional
3. Suggest the most appropriate category from: ${CATEGORIES.join(", ")}
4. Ensure the province is one of: ${PROVINCES.join(", ")}
5. Format phone numbers properly (+63 format)
6. Assign a quality score (1-10) based on completeness and professionalism
7. Suggest improvements if needed

Return as JSON array with this structure:
{
  "suppliers": [
    {
      "name": "Business Name",
      "description": "Original description",
      "category": "Suggested category",
      "location": "City, Province",
      "province": "Province name",
      "phone": "+63 ************",
      "email": "<EMAIL>",
      "website": "https://website.com",
      "imageUrl": "image_url",
      "startingPrice": 15000,
      "verified": false,
      "featured": false,
      "aiSuggestions": {
        "enhancedDescription": "AI-improved description",
        "suggestedCategory": "Best category match",
        "qualityScore": 8,
        "improvements": ["suggestion1", "suggestion2"]
      }
    }
  ]
}
`;

      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        response_format: { type: "json_object" },
        temperature: 0.3,
      });

      const result = JSON.parse(response.choices[0].message.content || '{"suppliers": []}');
      return result.suppliers || [];
    } catch (error) {
      console.error("AI CSV processing error:", error);
      throw new Error("Failed to process CSV with AI");
    }
  }

  // Enhance individual supplier data
  async enhanceSupplierData(supplier: SupplierData): Promise<AIEnhancedSupplier> {
    try {
      const prompt = `
Enhance this supplier data for Hello Luzon! marketplace in Region 2, Philippines:

Supplier Data:
${JSON.stringify(supplier, null, 2)}

Available categories: ${CATEGORIES.join(", ")}
Available provinces: ${PROVINCES.join(", ")}

Provide:
1. Enhanced, professional description that's appealing to customers
2. Best category match
3. Quality score (1-10)
4. Specific improvement suggestions
5. Validate all contact information

Return JSON format:
{
  "enhancedDescription": "Professional description",
  "suggestedCategory": "Best category",
  "qualityScore": 8,
  "improvements": ["improvement suggestions"]
}
`;

      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        response_format: { type: "json_object" },
        temperature: 0.3,
      });

      const aiSuggestions = JSON.parse(response.choices[0].message.content || '{}');
      
      return {
        ...supplier,
        aiSuggestions
      };
    } catch (error) {
      console.error("AI enhancement error:", error);
      return supplier;
    }
  }

  // Smart search with natural language processing
  async smartSearch(query: string, suppliers: SupplierData[]): Promise<{
    results: SupplierData[];
    searchIntent: string;
    suggestions: string[];
  }> {
    try {
      const prompt = `
User is searching for event suppliers in Region 2, Philippines with this query: "${query}"

Available suppliers:
${JSON.stringify(suppliers.map(s => ({ name: s.name, category: s.category, description: s.description })), null, 2)}

Analyze the search intent and provide:
1. Most relevant suppliers (return their names)
2. Search intent interpretation
3. Alternative search suggestions

Return JSON:
{
  "relevantSuppliers": ["supplier1", "supplier2"],
  "searchIntent": "User is looking for...",
  "suggestions": ["try searching for...", "you might also like..."]
}
`;

      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        response_format: { type: "json_object" },
        temperature: 0.3,
      });

      const result = JSON.parse(response.choices[0].message.content || '{}');
      
      const relevantSuppliers = suppliers.filter(s => 
        result.relevantSuppliers?.includes(s.name)
      );

      return {
        results: relevantSuppliers,
        searchIntent: result.searchIntent || "",
        suggestions: result.suggestions || []
      };
    } catch (error) {
      console.error("Smart search error:", error);
      return {
        results: suppliers.filter(s => 
          s.name.toLowerCase().includes(query.toLowerCase()) ||
          s.description.toLowerCase().includes(query.toLowerCase())
        ),
        searchIntent: "Basic text search",
        suggestions: []
      };
    }
  }

  // Generate supplier recommendations
  async getRecommendations(userPreferences: {
    eventType?: string;
    budget?: number;
    location?: string;
    style?: string;
  }, suppliers: SupplierData[]): Promise<{
    recommendations: SupplierData[];
    reasoning: string;
  }> {
    try {
      const model = genAI.getGenerativeModel({ model: "gemini-pro" });
      
      const prompt = `
Generate supplier recommendations for Hello Luzon! marketplace based on:

User Preferences:
${JSON.stringify(userPreferences, null, 2)}

Available Suppliers:
${JSON.stringify(suppliers, null, 2)}

Provide the best 5 supplier recommendations with reasoning.

Return JSON:
{
  "recommendedSuppliers": ["supplier1", "supplier2"],
  "reasoning": "Why these suppliers match the user's needs..."
}
`;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      // Parse JSON from Gemini response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        const recommendedSuppliers = suppliers.filter(s => 
          parsed.recommendedSuppliers?.includes(s.name)
        );
        
        return {
          recommendations: recommendedSuppliers,
          reasoning: parsed.reasoning || "AI-powered recommendations"
        };
      }
      
      return {
        recommendations: suppliers.slice(0, 5),
        reasoning: "Top suppliers based on your preferences"
      };
    } catch (error) {
      console.error("Recommendation error:", error);
      return {
        recommendations: suppliers.slice(0, 5),
        reasoning: "Featured suppliers for your event"
      };
    }
  }

  // Data quality analysis
  async analyzeDataQuality(suppliers: SupplierData[]): Promise<{
    overallScore: number;
    issues: Array<{ supplier: string; issues: string[] }>;
    suggestions: string[];
  }> {
    try {
      const prompt = `
Analyze the data quality of these event suppliers for Hello Luzon! marketplace:

${JSON.stringify(suppliers, null, 2)}

Check for:
- Missing or incomplete information
- Invalid contact details
- Poor descriptions
- Inconsistent formatting
- Category mismatches

Return JSON:
{
  "overallScore": 8.5,
  "issues": [
    {
      "supplier": "Business Name",
      "issues": ["Missing website", "Short description"]
    }
  ],
  "suggestions": ["Add more supplier photos", "Standardize phone formats"]
}
`;

      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        response_format: { type: "json_object" },
        temperature: 0.2,
      });

      return JSON.parse(response.choices[0].message.content || '{"overallScore": 7, "issues": [], "suggestions": []}');
    } catch (error) {
      console.error("Data quality analysis error:", error);
      return {
        overallScore: 7,
        issues: [],
        suggestions: ["Regular data quality checks recommended"]
      };
    }
  }
}

export const aiAssistant = new AIAssistant();